{"version": 3, "file": "flush-micro-tasks.js", "names": ["_timers", "require", "flushMicroTasks", "Promise", "resolve", "setImmediate"], "sources": ["../src/flush-micro-tasks.ts"], "sourcesContent": ["import { setImmediate } from './helpers/timers';\n\nexport function flushMicroTasks() {\n  return new Promise((resolve) => setImmediate(resolve));\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEO,SAASC,eAAeA,CAAA,EAAG;EAChC,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK,IAAAC,oBAAY,EAACD,OAAO,CAAC,CAAC;AACxD", "ignoreList": []}