{"version": 3, "file": "render-hook.js", "names": ["React", "_interopRequireWildcard", "require", "_render", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "renderHook", "hookToRender", "options", "initialProps", "renderOptions", "result", "createRef", "TestComponent", "hookProps", "renderResult", "useEffect", "current", "rerender", "componentRerender", "unmount", "renderInternal", "createElement"], "sources": ["../src/render-hook.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { renderInternal } from './render';\n\nexport type RenderHookResult<Result, Props> = {\n  rerender: (props: Props) => void;\n  result: React.MutableRefObject<Result>;\n  unmount: () => void;\n};\n\nexport type RenderHookOptions<Props> = {\n  /**\n   * The initial props to pass to the hook.\n   */\n  initialProps?: Props;\n\n  /**\n   * Pass a React Component as the wrapper option to have it rendered around the inner element. This is most useful for creating\n   * reusable custom render functions for common data providers.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  wrapper?: React.ComponentType<any>;\n\n  /**\n   * Set to `false` to disable concurrent rendering.\n   * Otherwise `renderHook` will default to concurrent rendering.\n   */\n  concurrentRoot?: boolean;\n};\n\nexport function renderHook<Result, Props>(\n  hookToRender: (props: Props) => Result,\n  options?: RenderHookOptions<Props>,\n): RenderHookResult<Result, Props> {\n  const { initialProps, ...renderOptions } = options ?? {};\n\n  const result: React.MutableRefObject<Result | null> = React.createRef();\n\n  function TestComponent({ hookProps }: { hookProps: Props }) {\n    const renderResult = hookToRender(hookProps);\n\n    React.useEffect(() => {\n      result.current = renderResult;\n    });\n\n    return null;\n  }\n\n  const { rerender: componentRerender, unmount } = renderInternal(\n    // @ts-expect-error since option can be undefined, initialProps can be undefined when it should'nt\n    <TestComponent hookProps={initialProps} />,\n    renderOptions,\n  );\n\n  function rerender(hookProps: Props) {\n    return componentRerender(<TestComponent hookProps={hookProps} />);\n  }\n\n  return {\n    // Result should already be set after the first render effects are run.\n    result: result as React.MutableRefObject<Result>,\n    rerender,\n    unmount,\n  };\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAA0C,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AA4BnC,SAASW,UAAUA,CACxBC,YAAsC,EACtCC,OAAkC,EACD;EACjC,MAAM;IAAEC,YAAY;IAAE,GAAGC;EAAc,CAAC,GAAGF,OAAO,IAAI,CAAC,CAAC;EAExD,MAAMG,MAA6C,gBAAG7B,KAAK,CAAC8B,SAAS,CAAC,CAAC;EAEvE,SAASC,aAAaA,CAAC;IAAEC;EAAgC,CAAC,EAAE;IAC1D,MAAMC,YAAY,GAAGR,YAAY,CAACO,SAAS,CAAC;IAE5ChC,KAAK,CAACkC,SAAS,CAAC,MAAM;MACpBL,MAAM,CAACM,OAAO,GAAGF,YAAY;IAC/B,CAAC,CAAC;IAEF,OAAO,IAAI;EACb;EAEA,MAAM;IAAEG,QAAQ,EAAEC,iBAAiB;IAAEC;EAAQ,CAAC,GAAG,IAAAC,sBAAc;EAAA;EAC7D;EACAvC,KAAA,CAAAwC,aAAA,CAACT,aAAa;IAACC,SAAS,EAAEL;EAAa,CAAE,CAAC,EAC1CC,aACF,CAAC;EAED,SAASQ,QAAQA,CAACJ,SAAgB,EAAE;IAClC,OAAOK,iBAAiB,cAACrC,KAAA,CAAAwC,aAAA,CAACT,aAAa;MAACC,SAAS,EAAEA;IAAU,CAAE,CAAC,CAAC;EACnE;EAEA,OAAO;IACL;IACAH,MAAM,EAAEA,MAAwC;IAChDO,QAAQ;IACRE;EACF,CAAC;AACH", "ignoreList": []}