{"version": 3, "file": "to-be-on-the-screen.js", "names": ["_jestMatcherU<PERSON>s", "require", "_redent", "_interopRequireDefault", "_componentTree", "_formatElement", "_screen", "_utils", "e", "__esModule", "default", "toBeOnTheScreen", "element", "isNot", "checkHostElement", "pass", "screen", "UNSAFE_root", "getUnsafeRootElement", "errorFound", "redent", "formatElement", "errorNotFound", "message", "matcherHint", "RECEIVED_COLOR", "join"], "sources": ["../../src/matchers/to-be-on-the-screen.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint, RECEIVED_COLOR } from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport { getUnsafeRootElement } from '../helpers/component-tree';\nimport { formatElement } from '../helpers/format-element';\nimport { screen } from '../screen';\nimport { checkHostElement } from './utils';\n\nexport function toBeOnTheScreen(this: jest.MatcherContext, element: ReactTestInstance) {\n  if (element !== null || !this.isNot) {\n    checkHostElement(element, toBeOnTheScreen, this);\n  }\n\n  const pass = element === null ? false : screen.UNSAFE_root === getUnsafeRootElement(element);\n\n  const errorFound = () => {\n    return `expected element tree not to contain element, but found\\n${redent(\n      formatElement(element),\n      2,\n    )}`;\n  };\n\n  const errorNotFound = () => {\n    return `element could not be found in the element tree`;\n  };\n\n  return {\n    pass,\n    message: () => {\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeOnTheScreen`, 'element', ''),\n        '',\n        RECEIVED_COLOR(this.isNot ? errorFound() : errorNotFound()),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AAA2C,SAAAE,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,SAASG,eAAeA,CAA4BC,OAA0B,EAAE;EACrF,IAAIA,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;IACnC,IAAAC,uBAAgB,EAACF,OAAO,EAAED,eAAe,EAAE,IAAI,CAAC;EAClD;EAEA,MAAMI,IAAI,GAAGH,OAAO,KAAK,IAAI,GAAG,KAAK,GAAGI,cAAM,CAACC,WAAW,KAAK,IAAAC,mCAAoB,EAACN,OAAO,CAAC;EAE5F,MAAMO,UAAU,GAAGA,CAAA,KAAM;IACvB,OAAO,4DAA4D,IAAAC,eAAM,EACvE,IAAAC,4BAAa,EAACT,OAAO,CAAC,EACtB,CACF,CAAC,EAAE;EACL,CAAC;EAED,MAAMU,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO,gDAAgD;EACzD,CAAC;EAED,OAAO;IACLP,IAAI;IACJQ,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACX,KAAK,GAAG,MAAM,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,EAAE,CAAC,EACzE,EAAE,EACF,IAAAY,gCAAc,EAAC,IAAI,CAACZ,KAAK,GAAGM,UAAU,CAAC,CAAC,GAAGG,aAAa,CAAC,CAAC,CAAC,CAC5D,CAACI,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}