{"version": 3, "file": "to-be-checked.js", "names": ["_jestMatcherU<PERSON>s", "require", "_redent", "_interopRequireDefault", "_accessibility", "_errors", "_formatElement", "_hostComponentNames", "_utils", "e", "__esModule", "default", "toBeChecked", "element", "checkHostElement", "isHostSwitch", "isSupportedAccessibilityElement", "ErrorWithStack", "pass", "computeAriaChecked", "message", "is", "isNot", "matcherHint", "redent", "formatElement", "join", "isAccessibilityElement", "role", "getRole", "rolesSupportingCheckedState"], "sources": ["../../src/matchers/to-be-checked.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport {\n  computeAriaChecked,\n  getRole,\n  isAccessibilityElement,\n  rolesSupportingCheckedState,\n} from '../helpers/accessibility';\nimport { ErrorWithStack } from '../helpers/errors';\nimport { formatElement } from '../helpers/format-element';\nimport { isHostSwitch } from '../helpers/host-component-names';\nimport { checkHostElement } from './utils';\n\nexport function toBeChecked(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeChecked, this);\n\n  if (!isHostSwitch(element) && !isSupportedAccessibilityElement(element)) {\n    throw new ErrorWithStack(\n      `toBeChecked() works only on host \"Switch\" elements or accessibility elements with \"checkbox\", \"radio\" or \"switch\" role.`,\n      toBeChecked,\n    );\n  }\n\n  return {\n    pass: computeAriaChecked(element) === true,\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeChecked`, 'element', ''),\n        '',\n        `Received element ${is} checked:`,\n        redent(formatElement(element), 2),\n      ].join('\\n');\n    },\n  };\n}\n\nfunction isSupportedAccessibilityElement(element: ReactTestInstance) {\n  if (!isAccessibilityElement(element)) {\n    return false;\n  }\n\n  const role = getRole(element);\n  return rolesSupportingCheckedState[role];\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AAMA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,mBAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AAA2C,SAAAE,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,SAASG,WAAWA,CAA4BC,OAA0B,EAAE;EACjF,IAAAC,uBAAgB,EAACD,OAAO,EAAED,WAAW,EAAE,IAAI,CAAC;EAE5C,IAAI,CAAC,IAAAG,gCAAY,EAACF,OAAO,CAAC,IAAI,CAACG,+BAA+B,CAACH,OAAO,CAAC,EAAE;IACvE,MAAM,IAAII,sBAAc,CACtB,yHAAyH,EACzHL,WACF,CAAC;EACH;EAEA,OAAO;IACLM,IAAI,EAAE,IAAAC,iCAAkB,EAACN,OAAO,CAAC,KAAK,IAAI;IAC1CO,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACD,KAAK,GAAG,MAAM,GAAG,EAAE,cAAc,EAAE,SAAS,EAAE,EAAE,CAAC,EACrE,EAAE,EACF,oBAAoBD,EAAE,WAAW,EACjC,IAAAG,eAAM,EAAC,IAAAC,4BAAa,EAACZ,OAAO,CAAC,EAAE,CAAC,CAAC,CAClC,CAACa,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEA,SAASV,+BAA+BA,CAACH,OAA0B,EAAE;EACnE,IAAI,CAAC,IAAAc,qCAAsB,EAACd,OAAO,CAAC,EAAE;IACpC,OAAO,KAAK;EACd;EAEA,MAAMe,IAAI,GAAG,IAAAC,sBAAO,EAAChB,OAAO,CAAC;EAC7B,OAAOiB,0CAA2B,CAACF,IAAI,CAAC;AAC1C", "ignoreList": []}