{"version": 3, "file": "to-be-empty-element.js", "names": ["_jestMatcherU<PERSON>s", "require", "_redent", "_interopRequireDefault", "_componentTree", "_formatElement", "_utils", "e", "__esModule", "default", "toBeEmptyElement", "element", "checkHostElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getHostChildren", "pass", "length", "message", "matcherHint", "isNot", "RECEIVED_COLOR", "redent", "formatElementList", "join"], "sources": ["../../src/matchers/to-be-empty-element.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint, RECEIVED_COLOR } from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport { getHostChildren } from '../helpers/component-tree';\nimport { formatElementList } from '../helpers/format-element';\nimport { checkHostElement } from './utils';\n\nexport function toBeEmptyElement(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeEmptyElement, this);\n\n  const hostChildren = getHostChildren(element);\n\n  return {\n    pass: hostChildren.length === 0,\n    message: () => {\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeEmptyElement`, 'element', ''),\n        '',\n        'Received:',\n        `${RECEIVED_COLOR(redent(formatElementList(hostChildren), 2))}`,\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAA2C,SAAAE,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,SAASG,gBAAgBA,CAA4BC,OAA0B,EAAE;EACtF,IAAAC,uBAAgB,EAACD,OAAO,EAAED,gBAAgB,EAAE,IAAI,CAAC;EAEjD,MAAMG,YAAY,GAAG,IAAAC,8BAAe,EAACH,OAAO,CAAC;EAE7C,OAAO;IACLI,IAAI,EAAEF,YAAY,CAACG,MAAM,KAAK,CAAC;IAC/BC,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,mBAAmB,EAAE,SAAS,EAAE,EAAE,CAAC,EAC1E,EAAE,EACF,WAAW,EACX,GAAG,IAAAC,gCAAc,EAAC,IAAAC,eAAM,EAAC,IAAAC,gCAAiB,EAACT,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAChE,CAACU,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}