{"version": 3, "file": "to-have-accessible-name.js", "names": ["_jestMatcherU<PERSON>s", "require", "_accessibility", "_matches", "_utils", "toHaveAccessibleName", "element", "expectedName", "options", "checkHostElement", "<PERSON><PERSON><PERSON>", "computeAccessibleName", "missingExpectedValue", "arguments", "length", "pass", "matches", "normalizer", "exact", "message", "formatMessage", "matcherHint", "isNot", "join"], "sources": ["../../src/matchers/to-have-accessible-name.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\n\nimport { computeAccessibleName } from '../helpers/accessibility';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport { matches } from '../matches';\nimport { checkHostElement, formatMessage } from './utils';\n\nexport function toHaveAccessibleName(\n  this: jest.MatcherContext,\n  element: ReactTestInstance,\n  expectedName?: TextMatch,\n  options?: TextMatchOptions,\n) {\n  checkHostElement(element, toHaveAccessibleName, this);\n\n  const receivedName = computeAccessibleName(element);\n  const missingExpectedValue = arguments.length === 1;\n\n  let pass = false;\n  if (missingExpectedValue) {\n    pass = receivedName !== '';\n  } else {\n    pass =\n      expectedName != null\n        ? matches(expectedName, receivedName, options?.normalizer, options?.exact)\n        : false;\n  }\n\n  return {\n    pass,\n    message: () => {\n      return [\n        formatMessage(\n          matcherHint(`${this.isNot ? '.not' : ''}.toHaveAccessibleName`, 'element', ''),\n          `Expected element ${this.isNot ? 'not to' : 'to'} have accessible name`,\n          expectedName,\n          'Received',\n          receivedName,\n        ),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AAEA,IAAAC,cAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEO,SAASI,oBAAoBA,CAElCC,OAA0B,EAC1BC,YAAwB,EACxBC,OAA0B,EAC1B;EACA,IAAAC,uBAAgB,EAACH,OAAO,EAAED,oBAAoB,EAAE,IAAI,CAAC;EAErD,MAAMK,YAAY,GAAG,IAAAC,oCAAqB,EAACL,OAAO,CAAC;EACnD,MAAMM,oBAAoB,GAAGC,SAAS,CAACC,MAAM,KAAK,CAAC;EAEnD,IAAIC,IAAI,GAAG,KAAK;EAChB,IAAIH,oBAAoB,EAAE;IACxBG,IAAI,GAAGL,YAAY,KAAK,EAAE;EAC5B,CAAC,MAAM;IACLK,IAAI,GACFR,YAAY,IAAI,IAAI,GAChB,IAAAS,gBAAO,EAACT,YAAY,EAAEG,YAAY,EAAEF,OAAO,EAAES,UAAU,EAAET,OAAO,EAAEU,KAAK,CAAC,GACxE,KAAK;EACb;EAEA,OAAO;IACLH,IAAI;IACJI,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,oBAAa,EACX,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,uBAAuB,EAAE,SAAS,EAAE,EAAE,CAAC,EAC9E,oBAAoB,IAAI,CAACA,KAAK,GAAG,QAAQ,GAAG,IAAI,uBAAuB,EACvEf,YAAY,EACZ,UAAU,EACVG,YACF,CAAC,CACF,CAACa,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}