{"version": 3, "file": "dispatch-event.js", "names": ["_act", "_interopRequireDefault", "require", "_event<PERSON><PERSON><PERSON>", "_componentTree", "e", "__esModule", "default", "dispatchEvent", "element", "eventName", "event", "isElementMounted", "handler", "getEventHandler", "act"], "sources": ["../../../src/user-event/utils/dispatch-event.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport act from '../../act';\nimport { getEventHandler } from '../../event-handler';\nimport { isElementMounted } from '../../helpers/component-tree';\n\n/**\n * Basic dispatch event function used by User Event module.\n *\n * @param element element trigger event on\n * @param eventName name of the event\n * @param event event payload(s)\n */\nexport function dispatchEvent(element: ReactTestInstance, eventName: string, ...event: unknown[]) {\n  if (!isElementMounted(element)) {\n    return;\n  }\n\n  const handler = getEventHandler(element, eventName);\n  if (!handler) {\n    return;\n  }\n\n  // This will be called synchronously.\n  void act(() => {\n    handler(...event);\n  });\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAAgE,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhE;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,aAAaA,CAACC,OAA0B,EAAEC,SAAiB,EAAE,GAAGC,KAAgB,EAAE;EAChG,IAAI,CAAC,IAAAC,+BAAgB,EAACH,OAAO,CAAC,EAAE;IAC9B;EACF;EAEA,MAAMI,OAAO,GAAG,IAAAC,6BAAe,EAACL,OAAO,EAAEC,SAAS,CAAC;EACnD,IAAI,CAACG,OAAO,EAAE;IACZ;EACF;;EAEA;EACA,KAAK,IAAAE,YAAG,EAAC,MAAM;IACbF,OAAO,CAAC,GAAGF,KAAK,CAAC;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}