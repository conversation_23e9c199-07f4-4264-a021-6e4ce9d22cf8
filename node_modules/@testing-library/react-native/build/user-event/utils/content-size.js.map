{"version": 3, "file": "content-size.js", "names": ["getTextContentSize", "text", "lines", "split", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "max", "map", "line", "length", "width", "height"], "sources": ["../../../src/user-event/utils/content-size.ts"], "sourcesContent": ["import type { Size } from '../../types';\n\n/**\n * Simple function for getting mock the size of given text.\n *\n * It works by calculating height based on number of lines and width based on\n * the longest line length. It does not take into account font size, font\n * family, as well as different letter sizes.\n *\n * @param text text to be measure\n * @returns width and height of the text\n */\nexport function getTextContentSize(text: string): Size {\n  const lines = text.split('\\n');\n  const maxLineLength = Math.max(...lines.map((line) => line.length));\n\n  return {\n    width: maxLineLength * 5,\n    height: lines.length * 16,\n  };\n}\n"], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,kBAAkBA,CAACC,IAAY,EAAQ;EACrD,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC;EAC9B,MAAMC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGJ,KAAK,CAACK,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,MAAM,CAAC,CAAC;EAEnE,OAAO;IACLC,KAAK,EAAEN,aAAa,GAAG,CAAC;IACxBO,MAAM,EAAET,KAAK,CAACO,MAAM,GAAG;EACzB,CAAC;AACH", "ignoreList": []}