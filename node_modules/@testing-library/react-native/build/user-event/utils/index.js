"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _contentSize = require("./content-size");
Object.keys(_contentSize).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _contentSize[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _contentSize[key];
    }
  });
});
var _dispatchEvent = require("./dispatch-event");
Object.keys(_dispatchEvent).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _dispatchEvent[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _dispatchEvent[key];
    }
  });
});
var _textRange = require("./text-range");
Object.keys(_textRange).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _textRange[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _textRange[key];
    }
  });
});
var _wait = require("./wait");
Object.keys(_wait).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _wait[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _wait[key];
    }
  });
});
//# sourceMappingURL=index.js.map