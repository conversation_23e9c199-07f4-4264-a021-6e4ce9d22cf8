{"version": 3, "file": "setup.js", "names": ["_timers", "require", "_wrapAsync", "_clear", "_paste", "_press", "_scroll", "_type", "_utils", "universalJestAdvanceTimersBy", "ms", "jestFakeTimersAreEnabled", "jest", "advanceTimersByTime", "Promise", "resolve", "defaultOptions", "delay", "advanceTimers", "setup", "options", "config", "createConfig", "instance", "createInstance", "api", "press", "wrapAndBindImpl", "longPress", "type", "clear", "paste", "scrollTo", "Object", "assign", "impl", "method", "args", "wrapAsync", "apply", "then", "result", "wait", "defineProperty", "get", "name"], "sources": ["../../../src/user-event/setup/setup.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { jestFakeTimersAreEnabled } from '../../helpers/timers';\nimport { wrapAsync } from '../../helpers/wrap-async';\nimport { clear } from '../clear';\nimport { paste } from '../paste';\nimport type { PressOptions } from '../press';\nimport { longPress, press } from '../press';\nimport type { ScrollToOptions } from '../scroll';\nimport { scrollTo } from '../scroll';\nimport type { TypeOptions } from '../type';\nimport { type } from '../type';\nimport { wait } from '../utils';\n\nexport interface UserEventSetupOptions {\n  /**\n   * Between some subsequent inputs like typing a series of characters\n   * the code execution is delayed per `setTimeout` for (at least) `delay` seconds.\n   * This moves the next changes at least to next macro task\n   * and allows other (asynchronous) code to run between events.\n   *\n   * `null` prevents `setTimeout` from being called.\n   *\n   * @default 0\n   */\n  delay?: number;\n\n  /**\n   * Function to be called to advance fake timers. Setting it is necessary for\n   * fake timers to work.\n   *\n   * @example jest.advanceTimersByTime\n   */\n  advanceTimers?: (delay: number) => Promise<void> | void;\n}\n\n/**\n * This functions allow wait to work correctly under both real and fake Jest timers.\n */\nfunction universalJestAdvanceTimersBy(ms: number) {\n  if (jestFakeTimersAreEnabled()) {\n    return jest.advanceTimersByTime(ms);\n  } else {\n    return Promise.resolve();\n  }\n}\n\nconst defaultOptions: Required<UserEventSetupOptions> = {\n  delay: 0,\n  advanceTimers: universalJestAdvanceTimersBy,\n};\n\n/**\n * Creates a new instance of user event instance with the given options.\n *\n * @param options\n * @returns UserEvent instance\n */\nexport function setup(options?: UserEventSetupOptions) {\n  const config = createConfig(options);\n  const instance = createInstance(config);\n  return instance;\n}\n\n/**\n * Options affecting all user event interactions.\n *\n * @param delay between some subsequent inputs like typing a series of characters\n * @param advanceTimers function to be called to advance fake timers\n */\nexport interface UserEventConfig {\n  delay: number;\n  advanceTimers: (delay: number) => Promise<void> | void;\n}\n\nfunction createConfig(options?: UserEventSetupOptions): UserEventConfig {\n  return {\n    ...defaultOptions,\n    ...options,\n  };\n}\n\n/**\n * UserEvent instance used to invoke user interaction functions.\n */\nexport interface UserEventInstance {\n  config: UserEventConfig;\n\n  press: (element: ReactTestInstance) => Promise<void>;\n  longPress: (element: ReactTestInstance, options?: PressOptions) => Promise<void>;\n\n  /**\n   * Simulate user pressing on a given `TextInput` element and typing given text.\n   *\n   * This method will trigger the events for each character of the text:\n   * `keyPress`, `change`, `changeText`, `endEditing`, etc.\n   *\n   * It will also trigger events connected with entering and leaving the text\n   * input.\n   *\n   * The exact events sent depend on the props of the TextInput (`editable`,\n   * `multiline`, etc) and passed options.\n   *\n   * @param element TextInput element to type on\n   * @param text Text to type\n   * @param options Options affecting typing behavior:\n   *  - `skipPress` - if true, `pressIn` and `pressOut` events will not be\n   *   triggered.\n   * - `submitEditing` - if true, `submitEditing` event will be triggered after\n   * typing the text.\n   */\n  type: (element: ReactTestInstance, text: string, options?: TypeOptions) => Promise<void>;\n\n  /**\n   * Simulate user clearing the text of a given `TextInput` element.\n   *\n   * This method will simulate:\n   * 1. entering TextInput\n   * 2. selecting all text\n   * 3. pressing backspace to delete all text\n   * 4. leaving TextInput\n   *\n   * @param element TextInput element to clear\n   */\n  clear: (element: ReactTestInstance) => Promise<void>;\n\n  /**\n   * Simulate user pasting the text to a given `TextInput` element.\n   *\n   * This method will simulate:\n   * 1. entering TextInput\n   * 2. selecting all text\n   * 3. paste the text\n   * 4. leaving TextInput\n   *\n   * @param element TextInput element to paste to\n   */\n  paste: (element: ReactTestInstance, text: string) => Promise<void>;\n\n  /**\n   * Simlate user scorlling a ScrollView element.\n   *\n   * @param element ScrollView element\n   * @returns\n   */\n  scrollTo: (element: ReactTestInstance, options: ScrollToOptions) => Promise<void>;\n}\n\nfunction createInstance(config: UserEventConfig): UserEventInstance {\n  const instance = {\n    config,\n  } as UserEventInstance;\n\n  // Bind interactions to given User Event instance.\n  const api = {\n    press: wrapAndBindImpl(instance, press),\n    longPress: wrapAndBindImpl(instance, longPress),\n    type: wrapAndBindImpl(instance, type),\n    clear: wrapAndBindImpl(instance, clear),\n    paste: wrapAndBindImpl(instance, paste),\n    scrollTo: wrapAndBindImpl(instance, scrollTo),\n  };\n\n  Object.assign(instance, api);\n  return instance;\n}\n\n/**\n * Wraps user interaction with `wrapAsync` (temporarily disable `act` environment while\n * calling & resolving the async callback, then flush the microtask queue)\n *\n * This implementation is sourced from `testing-library/user-event`\n * @see https://github.com/testing-library/user-event/blob/7a305dee9ab833d6f338d567fc2e862b4838b76a/src/setup/setup.ts#L121\n */\nfunction wrapAndBindImpl<\n  Args extends never[],\n  Impl extends (this: UserEventInstance, ...args: Args) => Promise<unknown>,\n>(instance: UserEventInstance, impl: Impl) {\n  function method(...args: Args) {\n    return wrapAsync(() =>\n      // eslint-disable-next-line promise/prefer-await-to-then\n      impl.apply(instance, args).then(async (result) => {\n        await wait(instance.config);\n        return result;\n      }),\n    );\n  }\n\n  // Copy implementation name to the returned function\n  Object.defineProperty(method, 'name', { get: () => impl.name });\n\n  return method as Impl;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAJ,OAAA;AAEA,IAAAK,OAAA,GAAAL,OAAA;AAEA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AAwBA;AACA;AACA;AACA,SAASQ,4BAA4BA,CAACC,EAAU,EAAE;EAChD,IAAI,IAAAC,gCAAwB,EAAC,CAAC,EAAE;IAC9B,OAAOC,IAAI,CAACC,mBAAmB,CAACH,EAAE,CAAC;EACrC,CAAC,MAAM;IACL,OAAOI,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;AACF;AAEA,MAAMC,cAA+C,GAAG;EACtDC,KAAK,EAAE,CAAC;EACRC,aAAa,EAAET;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,SAASU,KAAKA,CAACC,OAA+B,EAAE;EACrD,MAAMC,MAAM,GAAGC,YAAY,CAACF,OAAO,CAAC;EACpC,MAAMG,QAAQ,GAAGC,cAAc,CAACH,MAAM,CAAC;EACvC,OAAOE,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAMA,SAASD,YAAYA,CAACF,OAA+B,EAAmB;EACtE,OAAO;IACL,GAAGJ,cAAc;IACjB,GAAGI;EACL,CAAC;AACH;;AAEA;AACA;AACA;;AAgEA,SAASI,cAAcA,CAACH,MAAuB,EAAqB;EAClE,MAAME,QAAQ,GAAG;IACfF;EACF,CAAsB;;EAEtB;EACA,MAAMI,GAAG,GAAG;IACVC,KAAK,EAAEC,eAAe,CAACJ,QAAQ,EAAEG,YAAK,CAAC;IACvCE,SAAS,EAAED,eAAe,CAACJ,QAAQ,EAAEK,gBAAS,CAAC;IAC/CC,IAAI,EAAEF,eAAe,CAACJ,QAAQ,EAAEM,UAAI,CAAC;IACrCC,KAAK,EAAEH,eAAe,CAACJ,QAAQ,EAAEO,YAAK,CAAC;IACvCC,KAAK,EAAEJ,eAAe,CAACJ,QAAQ,EAAEQ,YAAK,CAAC;IACvCC,QAAQ,EAAEL,eAAe,CAACJ,QAAQ,EAAES,gBAAQ;EAC9C,CAAC;EAEDC,MAAM,CAACC,MAAM,CAACX,QAAQ,EAAEE,GAAG,CAAC;EAC5B,OAAOF,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAGtBJ,QAA2B,EAAEY,IAAU,EAAE;EACzC,SAASC,MAAMA,CAAC,GAAGC,IAAU,EAAE;IAC7B,OAAO,IAAAC,oBAAS,EAAC;IACf;IACAH,IAAI,CAACI,KAAK,CAAChB,QAAQ,EAAEc,IAAI,CAAC,CAACG,IAAI,CAAC,MAAOC,MAAM,IAAK;MAChD,MAAM,IAAAC,WAAI,EAACnB,QAAQ,CAACF,MAAM,CAAC;MAC3B,OAAOoB,MAAM;IACf,CAAC,CACH,CAAC;EACH;;EAEA;EACAR,MAAM,CAACU,cAAc,CAACP,MAAM,EAAE,MAAM,EAAE;IAAEQ,GAAG,EAAEA,CAAA,KAAMT,IAAI,CAACU;EAAK,CAAC,CAAC;EAE/D,OAAOT,MAAM;AACf", "ignoreList": []}