{"version": 3, "file": "utils.js", "names": ["DEFAULT_STEPS_COUNT", "createScrollSteps", "target", "initialOffset", "interpolator", "y", "map", "x", "linearInterpolator", "end", "start", "steps", "result", "i", "push", "lerp", "inertialInterpolator", "factor", "v0", "v1", "t"], "sources": ["../../../src/user-event/scroll/utils.ts"], "sourcesContent": ["import type { Point } from '../../types';\n\nconst DEFAULT_STEPS_COUNT = 5;\n\ntype InterpolatorFn = (end: number, start: number, steps: number) => number[];\n\nexport function createScrollSteps(\n  target: Partial<Point>,\n  initialOffset: Point,\n  interpolator: InterpolatorFn,\n): Point[] {\n  if (target.y != null) {\n    return interpolator(target.y, initialOffset.y, DEFAULT_STEPS_COUNT).map((y) => ({\n      y,\n      x: initialOffset.x,\n    }));\n  }\n\n  if (target.x != null) {\n    return interpolator(target.x, initialOffset.x, DEFAULT_STEPS_COUNT).map((x) => ({\n      x,\n      y: initialOffset.y,\n    }));\n  }\n\n  return [];\n}\n\n/**\n * Generate linear scroll values (with equal steps).\n */\nexport function linearInterpolator(end: number, start: number, steps: number): number[] {\n  if (end === start) {\n    return [end, start];\n  }\n\n  const result = [];\n  for (let i = 0; i < steps; i += 1) {\n    result.push(lerp(start, end, i / (steps - 1)));\n  }\n\n  return result;\n}\n\n/**\n * Generate inertial scroll values (exponentially slowing down).\n */\nexport function inertialInterpolator(end: number, start: number, steps: number): number[] {\n  if (end === start) {\n    return [end, start];\n  }\n\n  const result = [];\n  let factor = 1;\n  for (let i = 0; i < steps - 1; i += 1) {\n    result.push(lerp(end, start, factor));\n    factor /= 2;\n  }\n\n  result.push(end);\n  return result;\n}\n\n/**\n * Linear interpolation function\n * @param v0 initial value (when t = 0)\n * @param v1 final value (when t = 1)\n * @param t interpolation factor form 0 to 1\n * @returns interpolated value between v0 and v1\n */\nexport function lerp(v0: number, v1: number, t: number) {\n  return v0 + t * (v1 - v0);\n}\n"], "mappings": ";;;;;;;;;AAEA,MAAMA,mBAAmB,GAAG,CAAC;AAItB,SAASC,iBAAiBA,CAC/BC,MAAsB,EACtBC,aAAoB,EACpBC,YAA4B,EACnB;EACT,IAAIF,MAAM,CAACG,CAAC,IAAI,IAAI,EAAE;IACpB,OAAOD,YAAY,CAACF,MAAM,CAACG,CAAC,EAAEF,aAAa,CAACE,CAAC,EAAEL,mBAAmB,CAAC,CAACM,GAAG,CAAED,CAAC,KAAM;MAC9EA,CAAC;MACDE,CAAC,EAAEJ,aAAa,CAACI;IACnB,CAAC,CAAC,CAAC;EACL;EAEA,IAAIL,MAAM,CAACK,CAAC,IAAI,IAAI,EAAE;IACpB,OAAOH,YAAY,CAACF,MAAM,CAACK,CAAC,EAAEJ,aAAa,CAACI,CAAC,EAAEP,mBAAmB,CAAC,CAACM,GAAG,CAAEC,CAAC,KAAM;MAC9EA,CAAC;MACDF,CAAC,EAAEF,aAAa,CAACE;IACnB,CAAC,CAAC,CAAC;EACL;EAEA,OAAO,EAAE;AACX;;AAEA;AACA;AACA;AACO,SAASG,kBAAkBA,CAACC,GAAW,EAAEC,KAAa,EAAEC,KAAa,EAAY;EACtF,IAAIF,GAAG,KAAKC,KAAK,EAAE;IACjB,OAAO,CAACD,GAAG,EAAEC,KAAK,CAAC;EACrB;EAEA,MAAME,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,IAAI,CAAC,EAAE;IACjCD,MAAM,CAACE,IAAI,CAACC,IAAI,CAACL,KAAK,EAAED,GAAG,EAAEI,CAAC,IAAIF,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD;EAEA,OAAOC,MAAM;AACf;;AAEA;AACA;AACA;AACO,SAASI,oBAAoBA,CAACP,GAAW,EAAEC,KAAa,EAAEC,KAAa,EAAY;EACxF,IAAIF,GAAG,KAAKC,KAAK,EAAE;IACjB,OAAO,CAACD,GAAG,EAAEC,KAAK,CAAC;EACrB;EAEA,MAAME,MAAM,GAAG,EAAE;EACjB,IAAIK,MAAM,GAAG,CAAC;EACd,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE;IACrCD,MAAM,CAACE,IAAI,CAACC,IAAI,CAACN,GAAG,EAAEC,KAAK,EAAEO,MAAM,CAAC,CAAC;IACrCA,MAAM,IAAI,CAAC;EACb;EAEAL,MAAM,CAACE,IAAI,CAACL,GAAG,CAAC;EAChB,OAAOG,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,IAAIA,CAACG,EAAU,EAAEC,EAAU,EAAEC,CAAS,EAAE;EACtD,OAAOF,EAAE,GAAGE,CAAC,IAAID,EAAE,GAAGD,EAAE,CAAC;AAC3B", "ignoreList": []}