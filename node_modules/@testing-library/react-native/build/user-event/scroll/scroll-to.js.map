{"version": 3, "file": "scroll-to.js", "names": ["_jestMatcherU<PERSON>s", "require", "_errors", "_hostComponentNames", "_object", "_nativeState", "_eventBuilder", "_utils", "_utils2", "scrollTo", "element", "options", "isHostScrollView", "ErrorWithStack", "type", "ensureScrollViewDirection", "dispatchEvent", "contentSize", "width", "height", "initialOffset", "nativeState", "contentOffsetForElement", "get", "x", "y", "dragSteps", "createScrollSteps", "linearInterpolator", "emitDragScrollEvents", "config", "momentumStart", "at", "momentumSteps", "momentumY", "momentumX", "inertialInterpolator", "emitMomentumScrollEvents", "finalOffset", "set", "scrollSteps", "scrollOptions", "length", "wait", "EventBuilder", "ScrollView", "scroll", "i", "lastStep", "isVerticalScrollView", "props", "horizontal", "hasHorizontalScrollOptions", "undefined", "stringify", "pick", "hasVerticalScrollOptions"], "sources": ["../../../src/user-event/scroll/scroll-to.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { stringify } from 'jest-matcher-utils';\n\nimport { ErrorWithStack } from '../../helpers/errors';\nimport { isHostScrollView } from '../../helpers/host-component-names';\nimport { pick } from '../../helpers/object';\nimport { nativeState } from '../../native-state';\nimport type { Point, Size } from '../../types';\nimport { EventBuilder } from '../event-builder';\nimport type { UserEventConfig, UserEventInstance } from '../setup';\nimport { dispatchEvent, wait } from '../utils';\nimport { createScrollSteps, inertialInterpolator, linearInterpolator } from './utils';\n\ninterface CommonScrollToOptions {\n  contentSize?: Size;\n  layoutMeasurement?: Size;\n}\n\nexport interface VerticalScrollToOptions extends CommonScrollToOptions {\n  y: number;\n  momentumY?: number;\n\n  // Vertical scroll should not contain horizontal scroll part.\n  x?: never;\n  momentumX?: never;\n}\n\nexport interface HorizontalScrollToOptions extends CommonScrollToOptions {\n  x: number;\n  momentumX?: number;\n\n  // Horizontal scroll should not contain vertical scroll part.\n  y?: never;\n  momentumY?: never;\n}\n\nexport type ScrollToOptions = VerticalScrollToOptions | HorizontalScrollToOptions;\n\nexport async function scrollTo(\n  this: UserEventInstance,\n  element: ReactTestInstance,\n  options: ScrollToOptions,\n): Promise<void> {\n  if (!isHostScrollView(element)) {\n    throw new ErrorWithStack(\n      `scrollTo() works only with host \"ScrollView\" elements. Passed element has type \"${element.type}\".`,\n      scrollTo,\n    );\n  }\n\n  ensureScrollViewDirection(element, options);\n\n  dispatchEvent(\n    element,\n    'contentSizeChange',\n    options.contentSize?.width ?? 0,\n    options.contentSize?.height ?? 0,\n  );\n\n  const initialOffset = nativeState.contentOffsetForElement.get(element) ?? { x: 0, y: 0 };\n  const dragSteps = createScrollSteps(\n    { y: options.y, x: options.x },\n    initialOffset,\n    linearInterpolator,\n  );\n  await emitDragScrollEvents(this.config, element, dragSteps, options);\n\n  const momentumStart = dragSteps.at(-1) ?? initialOffset;\n  const momentumSteps = createScrollSteps(\n    { y: options.momentumY, x: options.momentumX },\n    momentumStart,\n    inertialInterpolator,\n  );\n  await emitMomentumScrollEvents(this.config, element, momentumSteps, options);\n\n  const finalOffset = momentumSteps.at(-1) ?? dragSteps.at(-1) ?? initialOffset;\n  nativeState.contentOffsetForElement.set(element, finalOffset);\n}\n\nasync function emitDragScrollEvents(\n  config: UserEventConfig,\n  element: ReactTestInstance,\n  scrollSteps: Point[],\n  scrollOptions: ScrollToOptions,\n) {\n  if (scrollSteps.length === 0) {\n    return;\n  }\n\n  await wait(config);\n  dispatchEvent(\n    element,\n    'scrollBeginDrag',\n    EventBuilder.ScrollView.scroll(scrollSteps[0], scrollOptions),\n  );\n\n  // Note: experimentally, in case of drag scroll the last scroll step\n  // will not trigger `scroll` event.\n  // See: https://github.com/callstack/react-native-testing-library/wiki/ScrollView-Events\n  for (let i = 1; i < scrollSteps.length - 1; i += 1) {\n    await wait(config);\n    dispatchEvent(element, 'scroll', EventBuilder.ScrollView.scroll(scrollSteps[i], scrollOptions));\n  }\n\n  await wait(config);\n  const lastStep = scrollSteps.at(-1);\n  dispatchEvent(element, 'scrollEndDrag', EventBuilder.ScrollView.scroll(lastStep, scrollOptions));\n}\n\nasync function emitMomentumScrollEvents(\n  config: UserEventConfig,\n  element: ReactTestInstance,\n  scrollSteps: Point[],\n  scrollOptions: ScrollToOptions,\n) {\n  if (scrollSteps.length === 0) {\n    return;\n  }\n\n  await wait(config);\n  dispatchEvent(\n    element,\n    'momentumScrollBegin',\n    EventBuilder.ScrollView.scroll(scrollSteps[0], scrollOptions),\n  );\n\n  // Note: experimentally, in case of momentum scroll the last scroll step\n  // will trigger `scroll` event.\n  // See: https://github.com/callstack/react-native-testing-library/wiki/ScrollView-Events\n  for (let i = 1; i < scrollSteps.length; i += 1) {\n    await wait(config);\n    dispatchEvent(element, 'scroll', EventBuilder.ScrollView.scroll(scrollSteps[i], scrollOptions));\n  }\n\n  await wait(config);\n  const lastStep = scrollSteps.at(-1);\n  dispatchEvent(\n    element,\n    'momentumScrollEnd',\n    EventBuilder.ScrollView.scroll(lastStep, scrollOptions),\n  );\n}\n\nfunction ensureScrollViewDirection(element: ReactTestInstance, options: ScrollToOptions) {\n  const isVerticalScrollView = element.props.horizontal !== true;\n\n  const hasHorizontalScrollOptions = options.x !== undefined || options.momentumX !== undefined;\n  if (isVerticalScrollView && hasHorizontalScrollOptions) {\n    throw new ErrorWithStack(\n      `scrollTo() expected only vertical scroll options: \"y\" and \"momentumY\" for vertical \"ScrollView\" element but received ${stringify(\n        pick(options, ['x', 'momentumX']),\n      )}`,\n      scrollTo,\n    );\n  }\n\n  const hasVerticalScrollOptions = options.y !== undefined || options.momentumY !== undefined;\n  if (!isVerticalScrollView && hasVerticalScrollOptions) {\n    throw new ErrorWithStack(\n      `scrollTo() expected only horizontal scroll options: \"x\" and \"momentumX\" for horizontal \"ScrollView\" element but received ${stringify(\n        pick(options, ['y', 'momentumY']),\n      )}`,\n      scrollTo,\n    );\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AAEA,IAAAK,aAAA,GAAAL,OAAA;AAEA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AA2BO,eAAeQ,QAAQA,CAE5BC,OAA0B,EAC1BC,OAAwB,EACT;EACf,IAAI,CAAC,IAAAC,oCAAgB,EAACF,OAAO,CAAC,EAAE;IAC9B,MAAM,IAAIG,sBAAc,CACtB,mFAAmFH,OAAO,CAACI,IAAI,IAAI,EACnGL,QACF,CAAC;EACH;EAEAM,yBAAyB,CAACL,OAAO,EAAEC,OAAO,CAAC;EAE3C,IAAAK,oBAAa,EACXN,OAAO,EACP,mBAAmB,EACnBC,OAAO,CAACM,WAAW,EAAEC,KAAK,IAAI,CAAC,EAC/BP,OAAO,CAACM,WAAW,EAAEE,MAAM,IAAI,CACjC,CAAC;EAED,MAAMC,aAAa,GAAGC,wBAAW,CAACC,uBAAuB,CAACC,GAAG,CAACb,OAAO,CAAC,IAAI;IAAEc,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EACxF,MAAMC,SAAS,GAAG,IAAAC,yBAAiB,EACjC;IAAEF,CAAC,EAAEd,OAAO,CAACc,CAAC;IAAED,CAAC,EAAEb,OAAO,CAACa;EAAE,CAAC,EAC9BJ,aAAa,EACbQ,0BACF,CAAC;EACD,MAAMC,oBAAoB,CAAC,IAAI,CAACC,MAAM,EAAEpB,OAAO,EAAEgB,SAAS,EAAEf,OAAO,CAAC;EAEpE,MAAMoB,aAAa,GAAGL,SAAS,CAACM,EAAE,CAAC,CAAC,CAAC,CAAC,IAAIZ,aAAa;EACvD,MAAMa,aAAa,GAAG,IAAAN,yBAAiB,EACrC;IAAEF,CAAC,EAAEd,OAAO,CAACuB,SAAS;IAAEV,CAAC,EAAEb,OAAO,CAACwB;EAAU,CAAC,EAC9CJ,aAAa,EACbK,4BACF,CAAC;EACD,MAAMC,wBAAwB,CAAC,IAAI,CAACP,MAAM,EAAEpB,OAAO,EAAEuB,aAAa,EAAEtB,OAAO,CAAC;EAE5E,MAAM2B,WAAW,GAAGL,aAAa,CAACD,EAAE,CAAC,CAAC,CAAC,CAAC,IAAIN,SAAS,CAACM,EAAE,CAAC,CAAC,CAAC,CAAC,IAAIZ,aAAa;EAC7EC,wBAAW,CAACC,uBAAuB,CAACiB,GAAG,CAAC7B,OAAO,EAAE4B,WAAW,CAAC;AAC/D;AAEA,eAAeT,oBAAoBA,CACjCC,MAAuB,EACvBpB,OAA0B,EAC1B8B,WAAoB,EACpBC,aAA8B,EAC9B;EACA,IAAID,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;IAC5B;EACF;EAEA,MAAM,IAAAC,WAAI,EAACb,MAAM,CAAC;EAClB,IAAAd,oBAAa,EACXN,OAAO,EACP,iBAAiB,EACjBkC,0BAAY,CAACC,UAAU,CAACC,MAAM,CAACN,WAAW,CAAC,CAAC,CAAC,EAAEC,aAAa,CAC9D,CAAC;;EAED;EACA;EACA;EACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,WAAW,CAACE,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAE;IAClD,MAAM,IAAAJ,WAAI,EAACb,MAAM,CAAC;IAClB,IAAAd,oBAAa,EAACN,OAAO,EAAE,QAAQ,EAAEkC,0BAAY,CAACC,UAAU,CAACC,MAAM,CAACN,WAAW,CAACO,CAAC,CAAC,EAAEN,aAAa,CAAC,CAAC;EACjG;EAEA,MAAM,IAAAE,WAAI,EAACb,MAAM,CAAC;EAClB,MAAMkB,QAAQ,GAAGR,WAAW,CAACR,EAAE,CAAC,CAAC,CAAC,CAAC;EACnC,IAAAhB,oBAAa,EAACN,OAAO,EAAE,eAAe,EAAEkC,0BAAY,CAACC,UAAU,CAACC,MAAM,CAACE,QAAQ,EAAEP,aAAa,CAAC,CAAC;AAClG;AAEA,eAAeJ,wBAAwBA,CACrCP,MAAuB,EACvBpB,OAA0B,EAC1B8B,WAAoB,EACpBC,aAA8B,EAC9B;EACA,IAAID,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;IAC5B;EACF;EAEA,MAAM,IAAAC,WAAI,EAACb,MAAM,CAAC;EAClB,IAAAd,oBAAa,EACXN,OAAO,EACP,qBAAqB,EACrBkC,0BAAY,CAACC,UAAU,CAACC,MAAM,CAACN,WAAW,CAAC,CAAC,CAAC,EAAEC,aAAa,CAC9D,CAAC;;EAED;EACA;EACA;EACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,WAAW,CAACE,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;IAC9C,MAAM,IAAAJ,WAAI,EAACb,MAAM,CAAC;IAClB,IAAAd,oBAAa,EAACN,OAAO,EAAE,QAAQ,EAAEkC,0BAAY,CAACC,UAAU,CAACC,MAAM,CAACN,WAAW,CAACO,CAAC,CAAC,EAAEN,aAAa,CAAC,CAAC;EACjG;EAEA,MAAM,IAAAE,WAAI,EAACb,MAAM,CAAC;EAClB,MAAMkB,QAAQ,GAAGR,WAAW,CAACR,EAAE,CAAC,CAAC,CAAC,CAAC;EACnC,IAAAhB,oBAAa,EACXN,OAAO,EACP,mBAAmB,EACnBkC,0BAAY,CAACC,UAAU,CAACC,MAAM,CAACE,QAAQ,EAAEP,aAAa,CACxD,CAAC;AACH;AAEA,SAAS1B,yBAAyBA,CAACL,OAA0B,EAAEC,OAAwB,EAAE;EACvF,MAAMsC,oBAAoB,GAAGvC,OAAO,CAACwC,KAAK,CAACC,UAAU,KAAK,IAAI;EAE9D,MAAMC,0BAA0B,GAAGzC,OAAO,CAACa,CAAC,KAAK6B,SAAS,IAAI1C,OAAO,CAACwB,SAAS,KAAKkB,SAAS;EAC7F,IAAIJ,oBAAoB,IAAIG,0BAA0B,EAAE;IACtD,MAAM,IAAIvC,sBAAc,CACtB,wHAAwH,IAAAyC,2BAAS,EAC/H,IAAAC,YAAI,EAAC5C,OAAO,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC,CAClC,CAAC,EAAE,EACHF,QACF,CAAC;EACH;EAEA,MAAM+C,wBAAwB,GAAG7C,OAAO,CAACc,CAAC,KAAK4B,SAAS,IAAI1C,OAAO,CAACuB,SAAS,KAAKmB,SAAS;EAC3F,IAAI,CAACJ,oBAAoB,IAAIO,wBAAwB,EAAE;IACrD,MAAM,IAAI3C,sBAAc,CACtB,4HAA4H,IAAAyC,2BAAS,EACnI,IAAAC,YAAI,EAAC5C,OAAO,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC,CAClC,CAAC,EAAE,EACHF,QACF,CAAC;EACH;AACF", "ignoreList": []}