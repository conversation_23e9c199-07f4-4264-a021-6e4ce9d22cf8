{"version": 3, "file": "type.js", "names": ["_errors", "require", "_hostComponentNames", "_pointerEvents", "_textInput", "_nativeState", "_eventBuilder", "_utils", "_parseKeys", "type", "element", "text", "options", "isHostTextInput", "ErrorWithStack", "isEditableTextInput", "isPointerEventEnabled", "keys", "parse<PERSON>eys", "skip<PERSON>ress", "dispatchEvent", "EventBuilder", "Common", "touch", "focus", "wait", "config", "currentText", "getTextInputValue", "key", "previousText", "proposedText", "<PERSON><PERSON><PERSON>", "isAccepted", "isTextChangeAccepted", "emitTypingEvents", "finalText", "submitEditing", "TextInput", "<PERSON><PERSON><PERSON><PERSON>", "endEditing", "blur", "isMultiline", "props", "multiline", "keyPress", "nativeState", "valueForElement", "set", "change", "<PERSON><PERSON><PERSON><PERSON>", "start", "length", "end", "selectionChange", "contentSize", "getTextContentSize", "contentSizeChange", "slice", "max<PERSON><PERSON><PERSON>", "undefined"], "sources": ["../../../src/user-event/type/type.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { ErrorWithStack } from '../../helpers/errors';\nimport { isHostTextInput } from '../../helpers/host-component-names';\nimport { isPointerEventEnabled } from '../../helpers/pointer-events';\nimport { getTextInputValue, isEditableTextInput } from '../../helpers/text-input';\nimport { nativeState } from '../../native-state';\nimport { EventBuilder } from '../event-builder';\nimport type { UserEventConfig, UserEventInstance } from '../setup';\nimport { dispatchEvent, getTextContentSize, wait } from '../utils';\nimport { parseKeys } from './parse-keys';\n\nexport interface TypeOptions {\n  skipPress?: boolean;\n  submitEditing?: boolean;\n  skipBlur?: boolean;\n}\n\nexport async function type(\n  this: UserEventInstance,\n  element: ReactTestInstance,\n  text: string,\n  options?: TypeOptions,\n): Promise<void> {\n  if (!isHostTextInput(element)) {\n    throw new ErrorWithStack(\n      `type() works only with host \"TextInput\" elements. Passed element has type \"${element.type}\".`,\n      type,\n    );\n  }\n\n  // Skip events if the element is disabled\n  if (!isEditableTextInput(element) || !isPointerEventEnabled(element)) {\n    return;\n  }\n\n  const keys = parseKeys(text);\n\n  if (!options?.skipPress) {\n    dispatchEvent(element, 'pressIn', EventBuilder.Common.touch());\n  }\n\n  dispatchEvent(element, 'focus', EventBuilder.Common.focus());\n\n  if (!options?.skipPress) {\n    await wait(this.config);\n    dispatchEvent(element, 'pressOut', EventBuilder.Common.touch());\n  }\n\n  let currentText = getTextInputValue(element);\n  for (const key of keys) {\n    const previousText = getTextInputValue(element);\n    const proposedText = applyKey(previousText, key);\n    const isAccepted = isTextChangeAccepted(element, proposedText);\n    currentText = isAccepted ? proposedText : previousText;\n\n    await emitTypingEvents(element, {\n      config: this.config,\n      key,\n      text: currentText,\n      isAccepted,\n    });\n  }\n\n  const finalText = getTextInputValue(element);\n  await wait(this.config);\n\n  if (options?.submitEditing) {\n    dispatchEvent(element, 'submitEditing', EventBuilder.TextInput.submitEditing(finalText));\n  }\n\n  if (!options?.skipBlur) {\n    dispatchEvent(element, 'endEditing', EventBuilder.TextInput.endEditing(finalText));\n    dispatchEvent(element, 'blur', EventBuilder.Common.blur());\n  }\n}\n\ntype EmitTypingEventsContext = {\n  config: UserEventConfig;\n  key: string;\n  text: string;\n  isAccepted?: boolean;\n};\n\nexport async function emitTypingEvents(\n  element: ReactTestInstance,\n  { config, key, text, isAccepted }: EmitTypingEventsContext,\n) {\n  const isMultiline = element.props.multiline === true;\n\n  await wait(config);\n  dispatchEvent(element, 'keyPress', EventBuilder.TextInput.keyPress(key));\n\n  // Platform difference (based on experiments):\n  // - iOS and RN Web: TextInput emits only `keyPress` event when max length has been reached\n  // - Android: TextInputs does not emit any events\n  if (isAccepted === false) {\n    return;\n  }\n\n  nativeState.valueForElement.set(element, text);\n  dispatchEvent(element, 'change', EventBuilder.TextInput.change(text));\n  dispatchEvent(element, 'changeText', text);\n\n  const selectionRange = {\n    start: text.length,\n    end: text.length,\n  };\n  dispatchEvent(element, 'selectionChange', EventBuilder.TextInput.selectionChange(selectionRange));\n\n  // According to the docs only multiline TextInput emits contentSizeChange event\n  // @see: https://reactnative.dev/docs/textinput#oncontentsizechange\n  if (isMultiline) {\n    const contentSize = getTextContentSize(text);\n    dispatchEvent(\n      element,\n      'contentSizeChange',\n      EventBuilder.TextInput.contentSizeChange(contentSize),\n    );\n  }\n}\n\nfunction applyKey(text: string, key: string) {\n  if (key === 'Enter') {\n    return `${text}\\n`;\n  }\n\n  if (key === 'Backspace') {\n    return text.slice(0, -1);\n  }\n\n  return text + key;\n}\n\nfunction isTextChangeAccepted(element: ReactTestInstance, text: string) {\n  const maxLength = element.props.maxLength;\n  return maxLength === undefined || text.length <= maxLength;\n}\n"], "mappings": ";;;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AAEA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AAQO,eAAeQ,IAAIA,CAExBC,OAA0B,EAC1BC,IAAY,EACZC,OAAqB,EACN;EACf,IAAI,CAAC,IAAAC,mCAAe,EAACH,OAAO,CAAC,EAAE;IAC7B,MAAM,IAAII,sBAAc,CACtB,8EAA8EJ,OAAO,CAACD,IAAI,IAAI,EAC9FA,IACF,CAAC;EACH;;EAEA;EACA,IAAI,CAAC,IAAAM,8BAAmB,EAACL,OAAO,CAAC,IAAI,CAAC,IAAAM,oCAAqB,EAACN,OAAO,CAAC,EAAE;IACpE;EACF;EAEA,MAAMO,IAAI,GAAG,IAAAC,oBAAS,EAACP,IAAI,CAAC;EAE5B,IAAI,CAACC,OAAO,EAAEO,SAAS,EAAE;IACvB,IAAAC,oBAAa,EAACV,OAAO,EAAE,SAAS,EAAEW,0BAAY,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EAChE;EAEA,IAAAH,oBAAa,EAACV,OAAO,EAAE,OAAO,EAAEW,0BAAY,CAACC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;EAE5D,IAAI,CAACZ,OAAO,EAAEO,SAAS,EAAE;IACvB,MAAM,IAAAM,WAAI,EAAC,IAAI,CAACC,MAAM,CAAC;IACvB,IAAAN,oBAAa,EAACV,OAAO,EAAE,UAAU,EAAEW,0BAAY,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EACjE;EAEA,IAAII,WAAW,GAAG,IAAAC,4BAAiB,EAAClB,OAAO,CAAC;EAC5C,KAAK,MAAMmB,GAAG,IAAIZ,IAAI,EAAE;IACtB,MAAMa,YAAY,GAAG,IAAAF,4BAAiB,EAAClB,OAAO,CAAC;IAC/C,MAAMqB,YAAY,GAAGC,QAAQ,CAACF,YAAY,EAAED,GAAG,CAAC;IAChD,MAAMI,UAAU,GAAGC,oBAAoB,CAACxB,OAAO,EAAEqB,YAAY,CAAC;IAC9DJ,WAAW,GAAGM,UAAU,GAAGF,YAAY,GAAGD,YAAY;IAEtD,MAAMK,gBAAgB,CAACzB,OAAO,EAAE;MAC9BgB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBG,GAAG;MACHlB,IAAI,EAAEgB,WAAW;MACjBM;IACF,CAAC,CAAC;EACJ;EAEA,MAAMG,SAAS,GAAG,IAAAR,4BAAiB,EAAClB,OAAO,CAAC;EAC5C,MAAM,IAAAe,WAAI,EAAC,IAAI,CAACC,MAAM,CAAC;EAEvB,IAAId,OAAO,EAAEyB,aAAa,EAAE;IAC1B,IAAAjB,oBAAa,EAACV,OAAO,EAAE,eAAe,EAAEW,0BAAY,CAACiB,SAAS,CAACD,aAAa,CAACD,SAAS,CAAC,CAAC;EAC1F;EAEA,IAAI,CAACxB,OAAO,EAAE2B,QAAQ,EAAE;IACtB,IAAAnB,oBAAa,EAACV,OAAO,EAAE,YAAY,EAAEW,0BAAY,CAACiB,SAAS,CAACE,UAAU,CAACJ,SAAS,CAAC,CAAC;IAClF,IAAAhB,oBAAa,EAACV,OAAO,EAAE,MAAM,EAAEW,0BAAY,CAACC,MAAM,CAACmB,IAAI,CAAC,CAAC,CAAC;EAC5D;AACF;AASO,eAAeN,gBAAgBA,CACpCzB,OAA0B,EAC1B;EAAEgB,MAAM;EAAEG,GAAG;EAAElB,IAAI;EAAEsB;AAAoC,CAAC,EAC1D;EACA,MAAMS,WAAW,GAAGhC,OAAO,CAACiC,KAAK,CAACC,SAAS,KAAK,IAAI;EAEpD,MAAM,IAAAnB,WAAI,EAACC,MAAM,CAAC;EAClB,IAAAN,oBAAa,EAACV,OAAO,EAAE,UAAU,EAAEW,0BAAY,CAACiB,SAAS,CAACO,QAAQ,CAAChB,GAAG,CAAC,CAAC;;EAExE;EACA;EACA;EACA,IAAII,UAAU,KAAK,KAAK,EAAE;IACxB;EACF;EAEAa,wBAAW,CAACC,eAAe,CAACC,GAAG,CAACtC,OAAO,EAAEC,IAAI,CAAC;EAC9C,IAAAS,oBAAa,EAACV,OAAO,EAAE,QAAQ,EAAEW,0BAAY,CAACiB,SAAS,CAACW,MAAM,CAACtC,IAAI,CAAC,CAAC;EACrE,IAAAS,oBAAa,EAACV,OAAO,EAAE,YAAY,EAAEC,IAAI,CAAC;EAE1C,MAAMuC,cAAc,GAAG;IACrBC,KAAK,EAAExC,IAAI,CAACyC,MAAM;IAClBC,GAAG,EAAE1C,IAAI,CAACyC;EACZ,CAAC;EACD,IAAAhC,oBAAa,EAACV,OAAO,EAAE,iBAAiB,EAAEW,0BAAY,CAACiB,SAAS,CAACgB,eAAe,CAACJ,cAAc,CAAC,CAAC;;EAEjG;EACA;EACA,IAAIR,WAAW,EAAE;IACf,MAAMa,WAAW,GAAG,IAAAC,yBAAkB,EAAC7C,IAAI,CAAC;IAC5C,IAAAS,oBAAa,EACXV,OAAO,EACP,mBAAmB,EACnBW,0BAAY,CAACiB,SAAS,CAACmB,iBAAiB,CAACF,WAAW,CACtD,CAAC;EACH;AACF;AAEA,SAASvB,QAAQA,CAACrB,IAAY,EAAEkB,GAAW,EAAE;EAC3C,IAAIA,GAAG,KAAK,OAAO,EAAE;IACnB,OAAO,GAAGlB,IAAI,IAAI;EACpB;EAEA,IAAIkB,GAAG,KAAK,WAAW,EAAE;IACvB,OAAOlB,IAAI,CAAC+C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1B;EAEA,OAAO/C,IAAI,GAAGkB,GAAG;AACnB;AAEA,SAASK,oBAAoBA,CAACxB,OAA0B,EAAEC,IAAY,EAAE;EACtE,MAAMgD,SAAS,GAAGjD,OAAO,CAACiC,KAAK,CAACgB,SAAS;EACzC,OAAOA,SAAS,KAAKC,SAAS,IAAIjD,IAAI,CAACyC,MAAM,IAAIO,SAAS;AAC5D", "ignoreList": []}