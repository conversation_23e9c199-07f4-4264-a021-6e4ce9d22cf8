{"version": 3, "file": "press.js", "names": ["_act", "_interopRequireDefault", "require", "_event<PERSON><PERSON><PERSON>", "_componentTree", "_errors", "_hostComponentNames", "_pointerEvents", "_eventBuilder", "_utils", "e", "__esModule", "default", "DEFAULT_MIN_PRESS_DURATION", "exports", "DEFAULT_LONG_PRESS_DELAY_MS", "press", "element", "isHostElement", "ErrorWithStack", "type", "basePress", "config", "longPress", "options", "duration", "isEnabledHostElement", "hasPressEventHandler", "emitDirectPressEvents", "isEnabledTouchResponder", "emitPressabilityPressEvents", "hostParentElement", "getHostParent", "isPointerEventEnabled", "isHostText", "props", "disabled", "isHostTextInput", "editable", "onStartShouldSetResponder", "getEventHandler", "wait", "dispatchEvent", "EventBuilder", "Common", "touch", "responderGrant", "responderRelease", "act"], "sources": ["../../../src/user-event/press/press.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport act from '../../act';\nimport { getEventHandler } from '../../event-handler';\nimport type { HostTestInstance } from '../../helpers/component-tree';\nimport { getHostParent, isHostElement } from '../../helpers/component-tree';\nimport { ErrorWithStack } from '../../helpers/errors';\nimport { isHostText, isHostTextInput } from '../../helpers/host-component-names';\nimport { isPointerEventEnabled } from '../../helpers/pointer-events';\nimport { EventBuilder } from '../event-builder';\nimport type { UserEventConfig, UserEventInstance } from '../setup';\nimport { dispatchEvent, wait } from '../utils';\n\n// These are constants defined in the React Native repo\n// See: https://github.com/facebook/react-native/blob/50e38cc9f1e6713228a91ad50f426c4f65e65e1a/packages/react-native/Libraries/Pressability/Pressability.js#L264\nexport const DEFAULT_MIN_PRESS_DURATION = 130;\nexport const DEFAULT_LONG_PRESS_DELAY_MS = 500;\n\nexport interface PressOptions {\n  duration?: number;\n}\n\nexport async function press(this: UserEventInstance, element: ReactTestInstance): Promise<void> {\n  if (!isHostElement(element)) {\n    throw new ErrorWithStack(\n      `press() works only with host elements. Passed element has type \"${element.type}\".`,\n      press,\n    );\n  }\n\n  await basePress(this.config, element, {\n    type: 'press',\n  });\n}\n\nexport async function longPress(\n  this: UserEventInstance,\n  element: ReactTestInstance,\n  options?: PressOptions,\n): Promise<void> {\n  if (!isHostElement(element)) {\n    throw new ErrorWithStack(\n      `longPress() works only with host elements. Passed element has type \"${element.type}\".`,\n      longPress,\n    );\n  }\n\n  await basePress(this.config, element, {\n    type: 'longPress',\n    duration: options?.duration ?? DEFAULT_LONG_PRESS_DELAY_MS,\n  });\n}\n\ninterface BasePressOptions {\n  type: 'press' | 'longPress';\n  duration?: number;\n}\n\nconst basePress = async (\n  config: UserEventConfig,\n  element: HostTestInstance,\n  options: BasePressOptions,\n): Promise<void> => {\n  if (isEnabledHostElement(element) && hasPressEventHandler(element)) {\n    await emitDirectPressEvents(config, element, options);\n    return;\n  }\n\n  if (isEnabledTouchResponder(element)) {\n    await emitPressabilityPressEvents(config, element, options);\n    return;\n  }\n\n  const hostParentElement = getHostParent(element);\n  if (!hostParentElement) {\n    return;\n  }\n\n  await basePress(config, hostParentElement, options);\n};\n\nfunction isEnabledHostElement(element: HostTestInstance) {\n  if (!isPointerEventEnabled(element)) {\n    return false;\n  }\n\n  if (isHostText(element)) {\n    return element.props.disabled !== true;\n  }\n\n  if (isHostTextInput(element)) {\n    // @ts-expect-error - workaround incorrect ReactTestInstance type\n    return element.props.editable !== false;\n  }\n\n  return true;\n}\n\nfunction isEnabledTouchResponder(element: HostTestInstance) {\n  return isPointerEventEnabled(element) && element.props.onStartShouldSetResponder?.();\n}\n\nfunction hasPressEventHandler(element: HostTestInstance) {\n  return (\n    getEventHandler(element, 'press') ||\n    getEventHandler(element, 'longPress') ||\n    getEventHandler(element, 'pressIn') ||\n    getEventHandler(element, 'pressOut')\n  );\n}\n\n/**\n * Dispatches a press event sequence for host elements that have `onPress*` event handlers.\n */\nasync function emitDirectPressEvents(\n  config: UserEventConfig,\n  element: HostTestInstance,\n  options: BasePressOptions,\n) {\n  await wait(config);\n  dispatchEvent(element, 'pressIn', EventBuilder.Common.touch());\n\n  await wait(config, options.duration);\n\n  // Long press events are emitted before `pressOut`.\n  if (options.type === 'longPress') {\n    dispatchEvent(element, 'longPress', EventBuilder.Common.touch());\n  }\n\n  dispatchEvent(element, 'pressOut', EventBuilder.Common.touch());\n\n  // Regular press events are emitted after `pressOut` according to the React Native docs.\n  // See: https://reactnative.dev/docs/pressable#onpress\n  // Experimentally for very short presses (< 130ms) `press` events are actually emitted before `onPressOut`, but\n  // we will ignore that as in reality most pressed would be above the 130ms threshold.\n  if (options.type === 'press') {\n    dispatchEvent(element, 'press', EventBuilder.Common.touch());\n  }\n}\n\nasync function emitPressabilityPressEvents(\n  config: UserEventConfig,\n  element: HostTestInstance,\n  options: BasePressOptions,\n) {\n  await wait(config);\n\n  dispatchEvent(element, 'responderGrant', EventBuilder.Common.responderGrant());\n\n  const duration = options.duration ?? DEFAULT_MIN_PRESS_DURATION;\n  await wait(config, duration);\n\n  dispatchEvent(element, 'responderRelease', EventBuilder.Common.responderRelease());\n\n  // React Native will wait for minimal delay of DEFAULT_MIN_PRESS_DURATION\n  // before emitting the `pressOut` event. We need to wait here, so that\n  // `press()` function does not return before that.\n  if (DEFAULT_MIN_PRESS_DURATION - duration > 0) {\n    await act(() => wait(config, DEFAULT_MIN_PRESS_DURATION - duration));\n  }\n}\n"], "mappings": ";;;;;;;;AAEA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AAEA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,mBAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,aAAA,GAAAN,OAAA;AAEA,IAAAO,MAAA,GAAAP,OAAA;AAA+C,SAAAD,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE/C;AACA;AACO,MAAMG,0BAA0B,GAAAC,OAAA,CAAAD,0BAAA,GAAG,GAAG;AACtC,MAAME,2BAA2B,GAAAD,OAAA,CAAAC,2BAAA,GAAG,GAAG;AAMvC,eAAeC,KAAKA,CAA0BC,OAA0B,EAAiB;EAC9F,IAAI,CAAC,IAAAC,4BAAa,EAACD,OAAO,CAAC,EAAE;IAC3B,MAAM,IAAIE,sBAAc,CACtB,mEAAmEF,OAAO,CAACG,IAAI,IAAI,EACnFJ,KACF,CAAC;EACH;EAEA,MAAMK,SAAS,CAAC,IAAI,CAACC,MAAM,EAAEL,OAAO,EAAE;IACpCG,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AAEO,eAAeG,SAASA,CAE7BN,OAA0B,EAC1BO,OAAsB,EACP;EACf,IAAI,CAAC,IAAAN,4BAAa,EAACD,OAAO,CAAC,EAAE;IAC3B,MAAM,IAAIE,sBAAc,CACtB,uEAAuEF,OAAO,CAACG,IAAI,IAAI,EACvFG,SACF,CAAC;EACH;EAEA,MAAMF,SAAS,CAAC,IAAI,CAACC,MAAM,EAAEL,OAAO,EAAE;IACpCG,IAAI,EAAE,WAAW;IACjBK,QAAQ,EAAED,OAAO,EAAEC,QAAQ,IAAIV;EACjC,CAAC,CAAC;AACJ;AAOA,MAAMM,SAAS,GAAG,MAAAA,CAChBC,MAAuB,EACvBL,OAAyB,EACzBO,OAAyB,KACP;EAClB,IAAIE,oBAAoB,CAACT,OAAO,CAAC,IAAIU,oBAAoB,CAACV,OAAO,CAAC,EAAE;IAClE,MAAMW,qBAAqB,CAACN,MAAM,EAAEL,OAAO,EAAEO,OAAO,CAAC;IACrD;EACF;EAEA,IAAIK,uBAAuB,CAACZ,OAAO,CAAC,EAAE;IACpC,MAAMa,2BAA2B,CAACR,MAAM,EAAEL,OAAO,EAAEO,OAAO,CAAC;IAC3D;EACF;EAEA,MAAMO,iBAAiB,GAAG,IAAAC,4BAAa,EAACf,OAAO,CAAC;EAChD,IAAI,CAACc,iBAAiB,EAAE;IACtB;EACF;EAEA,MAAMV,SAAS,CAACC,MAAM,EAAES,iBAAiB,EAAEP,OAAO,CAAC;AACrD,CAAC;AAED,SAASE,oBAAoBA,CAACT,OAAyB,EAAE;EACvD,IAAI,CAAC,IAAAgB,oCAAqB,EAAChB,OAAO,CAAC,EAAE;IACnC,OAAO,KAAK;EACd;EAEA,IAAI,IAAAiB,8BAAU,EAACjB,OAAO,CAAC,EAAE;IACvB,OAAOA,OAAO,CAACkB,KAAK,CAACC,QAAQ,KAAK,IAAI;EACxC;EAEA,IAAI,IAAAC,mCAAe,EAACpB,OAAO,CAAC,EAAE;IAC5B;IACA,OAAOA,OAAO,CAACkB,KAAK,CAACG,QAAQ,KAAK,KAAK;EACzC;EAEA,OAAO,IAAI;AACb;AAEA,SAAST,uBAAuBA,CAACZ,OAAyB,EAAE;EAC1D,OAAO,IAAAgB,oCAAqB,EAAChB,OAAO,CAAC,IAAIA,OAAO,CAACkB,KAAK,CAACI,yBAAyB,GAAG,CAAC;AACtF;AAEA,SAASZ,oBAAoBA,CAACV,OAAyB,EAAE;EACvD,OACE,IAAAuB,6BAAe,EAACvB,OAAO,EAAE,OAAO,CAAC,IACjC,IAAAuB,6BAAe,EAACvB,OAAO,EAAE,WAAW,CAAC,IACrC,IAAAuB,6BAAe,EAACvB,OAAO,EAAE,SAAS,CAAC,IACnC,IAAAuB,6BAAe,EAACvB,OAAO,EAAE,UAAU,CAAC;AAExC;;AAEA;AACA;AACA;AACA,eAAeW,qBAAqBA,CAClCN,MAAuB,EACvBL,OAAyB,EACzBO,OAAyB,EACzB;EACA,MAAM,IAAAiB,WAAI,EAACnB,MAAM,CAAC;EAClB,IAAAoB,oBAAa,EAACzB,OAAO,EAAE,SAAS,EAAE0B,0BAAY,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EAE9D,MAAM,IAAAJ,WAAI,EAACnB,MAAM,EAAEE,OAAO,CAACC,QAAQ,CAAC;;EAEpC;EACA,IAAID,OAAO,CAACJ,IAAI,KAAK,WAAW,EAAE;IAChC,IAAAsB,oBAAa,EAACzB,OAAO,EAAE,WAAW,EAAE0B,0BAAY,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EAClE;EAEA,IAAAH,oBAAa,EAACzB,OAAO,EAAE,UAAU,EAAE0B,0BAAY,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;;EAE/D;EACA;EACA;EACA;EACA,IAAIrB,OAAO,CAACJ,IAAI,KAAK,OAAO,EAAE;IAC5B,IAAAsB,oBAAa,EAACzB,OAAO,EAAE,OAAO,EAAE0B,0BAAY,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EAC9D;AACF;AAEA,eAAef,2BAA2BA,CACxCR,MAAuB,EACvBL,OAAyB,EACzBO,OAAyB,EACzB;EACA,MAAM,IAAAiB,WAAI,EAACnB,MAAM,CAAC;EAElB,IAAAoB,oBAAa,EAACzB,OAAO,EAAE,gBAAgB,EAAE0B,0BAAY,CAACC,MAAM,CAACE,cAAc,CAAC,CAAC,CAAC;EAE9E,MAAMrB,QAAQ,GAAGD,OAAO,CAACC,QAAQ,IAAIZ,0BAA0B;EAC/D,MAAM,IAAA4B,WAAI,EAACnB,MAAM,EAAEG,QAAQ,CAAC;EAE5B,IAAAiB,oBAAa,EAACzB,OAAO,EAAE,kBAAkB,EAAE0B,0BAAY,CAACC,MAAM,CAACG,gBAAgB,CAAC,CAAC,CAAC;;EAElF;EACA;EACA;EACA,IAAIlC,0BAA0B,GAAGY,QAAQ,GAAG,CAAC,EAAE;IAC7C,MAAM,IAAAuB,YAAG,EAAC,MAAM,IAAAP,WAAI,EAACnB,MAAM,EAAET,0BAA0B,GAAGY,QAAQ,CAAC,CAAC;EACtE;AACF", "ignoreList": []}