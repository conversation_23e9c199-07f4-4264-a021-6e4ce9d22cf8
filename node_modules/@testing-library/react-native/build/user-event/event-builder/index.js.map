{"version": 3, "file": "index.js", "names": ["_common", "require", "_scrollView", "_textInput", "EventBuilder", "exports", "Common", "CommonEventBuilder", "ScrollView", "ScrollViewEventBuilder", "TextInput", "TextInputEventBuilder"], "sources": ["../../../src/user-event/event-builder/index.ts"], "sourcesContent": ["import { CommonEventBuilder } from './common';\nimport { ScrollViewEventBuilder } from './scroll-view';\nimport { TextInputEventBuilder } from './text-input';\n\nexport const EventBuilder = {\n  Common: CommonEventBuilder,\n  ScrollView: ScrollViewEventBuilder,\n  TextInput: TextInputEventBuilder,\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAEO,MAAMG,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAG;EAC1BE,MAAM,EAAEC,0BAAkB;EAC1BC,UAAU,EAAEC,kCAAsB;EAClCC,SAAS,EAAEC;AACb,CAAC", "ignoreList": []}