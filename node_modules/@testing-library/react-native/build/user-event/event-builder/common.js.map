{"version": 3, "file": "common.js", "names": ["_base", "require", "touch", "baseSyntheticEvent", "nativeEvent", "changedTouches", "identifier", "locationX", "locationY", "pageX", "pageY", "target", "timestamp", "Date", "now", "touches", "currentTarget", "measure", "CommonEventBuilder", "exports", "responderGrant", "dispatchConfig", "registrationName", "responderRelease", "focus", "blur"], "sources": ["../../../src/user-event/event-builder/common.ts"], "sourcesContent": ["import { baseSyntheticEvent } from './base';\n\n/**\n * Experimental values:\n * - iOS: `{\"changedTouches\": [[Circular]], \"identifier\": 1, \"locationX\": 253, \"locationY\": 30.333328247070312, \"pageX\": 273, \"pageY\": 141.3333282470703, \"target\": 75, \"timestamp\": 875928682.0450834, \"touches\": [[Circular]]}`\n * - Android: `{\"changedTouches\": [[Circular]], \"identifier\": 0, \"locationX\": 160, \"locationY\": 40.3636360168457, \"pageX\": 180, \"pageY\": 140.36363220214844, \"target\": 53, \"targetSurface\": -1, \"timestamp\": 10290805, \"touches\": [[Circular]]}`\n */\nfunction touch() {\n  return {\n    ...baseSyntheticEvent(),\n    nativeEvent: {\n      changedTouches: [],\n      identifier: 0,\n      locationX: 0,\n      locationY: 0,\n      pageX: 0,\n      pageY: 0,\n      target: 0,\n      timestamp: Date.now(),\n      touches: [],\n    },\n    currentTarget: { measure: () => {} },\n  };\n}\n\nexport const CommonEventBuilder = {\n  touch,\n\n  responderGrant: () => {\n    return {\n      ...touch(),\n      dispatchConfig: { registrationName: 'onResponderGrant' },\n    };\n  },\n\n  responderRelease: () => {\n    return {\n      ...touch(),\n      dispatchConfig: { registrationName: 'onResponderRelease' },\n    };\n  },\n\n  /**\n   * Experimental values:\n   * - iOS: `{\"eventCount\": 0, \"target\": 75, \"text\": \"\"}`\n   * - Android: `{\"target\": 53}`\n   */\n  focus: () => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: {\n        target: 0,\n      },\n    };\n  },\n\n  /**\n   * Experimental values:\n   * - iOS: `{\"eventCount\": 0, \"target\": 75, \"text\": \"\"}`\n   * - Android: `{\"target\": 53}`\n   */\n  blur: () => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: {\n        target: 0,\n      },\n    };\n  },\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAAA,EAAG;EACf,OAAO;IACL,GAAG,IAAAC,wBAAkB,EAAC,CAAC;IACvBC,WAAW,EAAE;MACXC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBC,OAAO,EAAE;IACX,CAAC;IACDC,aAAa,EAAE;MAAEC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAAE;EACrC,CAAC;AACH;AAEO,MAAMC,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG;EAChChB,KAAK;EAELkB,cAAc,EAAEA,CAAA,KAAM;IACpB,OAAO;MACL,GAAGlB,KAAK,CAAC,CAAC;MACVmB,cAAc,EAAE;QAAEC,gBAAgB,EAAE;MAAmB;IACzD,CAAC;EACH,CAAC;EAEDC,gBAAgB,EAAEA,CAAA,KAAM;IACtB,OAAO;MACL,GAAGrB,KAAK,CAAC,CAAC;MACVmB,cAAc,EAAE;QAAEC,gBAAgB,EAAE;MAAqB;IAC3D,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEE,KAAK,EAAEA,CAAA,KAAM;IACX,OAAO;MACL,GAAG,IAAArB,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QACXO,MAAM,EAAE;MACV;IACF,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEc,IAAI,EAAEA,CAAA,KAAM;IACV,OAAO;MACL,GAAG,IAAAtB,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QACXO,MAAM,EAAE;MACV;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}