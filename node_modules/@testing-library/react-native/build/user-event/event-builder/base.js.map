{"version": 3, "file": "base.js", "names": ["baseSyntheticEvent", "currentTarget", "target", "preventDefault", "isDefaultPrevented", "stopPropagation", "isPropagationStopped", "persist", "isPersistent", "timeStamp"], "sources": ["../../../src/user-event/event-builder/base.ts"], "sourcesContent": ["import type { BaseSyntheticEvent } from 'react';\n\n/** Builds base syntentic event stub, with prop values as inspected in RN runtime. */\nexport function baseSyntheticEvent(): Partial<BaseSyntheticEvent<object, unknown, unknown>> {\n  return {\n    currentTarget: {},\n    target: {},\n    preventDefault: () => {},\n    isDefaultPrevented: () => false,\n    stopPropagation: () => {},\n    isPropagationStopped: () => false,\n    persist: () => {},\n    // @ts-expect-error: `isPersistent` is not a standard prop, but it's used in RN runtime. See: https://react.dev/reference/react-dom/components/common#react-event-object-methods\n    isPersistent: () => false,\n    timeStamp: 0,\n  };\n}\n"], "mappings": ";;;;;;AAEA;AACO,SAASA,kBAAkBA,CAAA,EAA0D;EAC1F,OAAO;IACLC,aAAa,EAAE,CAAC,CAAC;IACjBC,MAAM,EAAE,CAAC,CAAC;IACVC,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAC;IACxBC,kBAAkB,EAAEA,CAAA,KAAM,KAAK;IAC/BC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAC;IACzBC,oBAAoB,EAAEA,CAAA,KAAM,KAAK;IACjCC,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAC;IACjB;IACAC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,SAAS,EAAE;EACb,CAAC;AACH", "ignoreList": []}