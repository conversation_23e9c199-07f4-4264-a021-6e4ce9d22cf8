{"version": 3, "file": "scroll-view.js", "names": ["_base", "require", "ScrollViewEventBuilder", "exports", "scroll", "offset", "y", "x", "options", "baseSyntheticEvent", "nativeEvent", "contentInset", "bottom", "left", "right", "top", "contentOffset", "contentSize", "height", "width", "layoutMeasurement", "responderIgnoreScroll", "target", "velocity"], "sources": ["../../../src/user-event/event-builder/scroll-view.ts"], "sourcesContent": ["import type { Point, Size } from '../../types';\nimport { baseSyntheticEvent } from './base';\n\n/**\n * Other options for constructing a scroll event.\n */\nexport type ScrollEventOptions = {\n  contentSize?: Size;\n  layoutMeasurement?: Size;\n};\n\n/**\n * Experimental values:\n * - iOS: `{\"contentInset\": {\"bottom\": 0, \"left\": 0, \"right\": 0, \"top\": 0}, \"contentOffset\": {\"x\": 0, \"y\": 5.333333333333333}, \"contentSize\": {\"height\": 1676.6666259765625, \"width\": 390}, \"layoutMeasurement\": {\"height\": 753, \"width\": 390}, \"zoomScale\": 1}`\n * - Android: `{\"contentInset\": {\"bottom\": 0, \"left\": 0, \"right\": 0, \"top\": 0}, \"contentOffset\": {\"x\": 0, \"y\": 31.619047164916992}, \"contentSize\": {\"height\": 1624.761962890625, \"width\": 411.4285583496094}, \"layoutMeasurement\": {\"height\": 785.5238037109375, \"width\": 411.4285583496094}, \"responderIgnoreScroll\": true, \"target\": 139, \"velocity\": {\"x\": -1.3633992671966553, \"y\": -1.3633992671966553}}`\n */\nexport const ScrollViewEventBuilder = {\n  scroll: (offset: Point = { y: 0, x: 0 }, options?: ScrollEventOptions) => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: {\n        contentInset: { bottom: 0, left: 0, right: 0, top: 0 },\n        contentOffset: { y: offset.y, x: offset.x },\n        contentSize: {\n          height: options?.contentSize?.height ?? 0,\n          width: options?.contentSize?.width ?? 0,\n        },\n        layoutMeasurement: {\n          height: options?.layoutMeasurement?.height ?? 0,\n          width: options?.layoutMeasurement?.width ?? 0,\n        },\n        responderIgnoreScroll: true,\n        target: 0,\n        velocity: { y: 0, x: 0 },\n      },\n    };\n  },\n};\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AAEA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACO,MAAMC,sBAAsB,GAAAC,OAAA,CAAAD,sBAAA,GAAG;EACpCE,MAAM,EAAEA,CAACC,MAAa,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,EAAEC,OAA4B,KAAK;IACxE,OAAO;MACL,GAAG,IAAAC,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QACXC,YAAY,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE;QAAE,CAAC;QACtDC,aAAa,EAAE;UAAEV,CAAC,EAAED,MAAM,CAACC,CAAC;UAAEC,CAAC,EAAEF,MAAM,CAACE;QAAE,CAAC;QAC3CU,WAAW,EAAE;UACXC,MAAM,EAAEV,OAAO,EAAES,WAAW,EAAEC,MAAM,IAAI,CAAC;UACzCC,KAAK,EAAEX,OAAO,EAAES,WAAW,EAAEE,KAAK,IAAI;QACxC,CAAC;QACDC,iBAAiB,EAAE;UACjBF,MAAM,EAAEV,OAAO,EAAEY,iBAAiB,EAAEF,MAAM,IAAI,CAAC;UAC/CC,KAAK,EAAEX,OAAO,EAAEY,iBAAiB,EAAED,KAAK,IAAI;QAC9C,CAAC;QACDE,qBAAqB,EAAE,IAAI;QAC3BC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE;UAAEjB,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE;MACzB;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}