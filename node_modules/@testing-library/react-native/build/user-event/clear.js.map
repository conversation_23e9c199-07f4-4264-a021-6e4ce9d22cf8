{"version": 3, "file": "clear.js", "names": ["_errors", "require", "_hostComponentNames", "_pointerEvents", "_textInput", "_eventBuilder", "_type", "_utils", "clear", "element", "isHostTextInput", "ErrorWithStack", "type", "isEditableTextInput", "isPointerEventEnabled", "dispatchEvent", "EventBuilder", "Common", "focus", "textToClear", "getTextInputValue", "<PERSON><PERSON><PERSON><PERSON>", "start", "end", "length", "TextInput", "selectionChange", "emptyText", "emitTypingEvents", "config", "key", "text", "wait", "endEditing", "blur"], "sources": ["../../src/user-event/clear.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { ErrorWithStack } from '../helpers/errors';\nimport { isHostTextInput } from '../helpers/host-component-names';\nimport { isPointerEventEnabled } from '../helpers/pointer-events';\nimport { getTextInputValue, isEditableTextInput } from '../helpers/text-input';\nimport { EventBuilder } from './event-builder';\nimport type { UserEventInstance } from './setup';\nimport { emitTypingEvents } from './type/type';\nimport { dispatchEvent, wait } from './utils';\n\nexport async function clear(this: UserEventInstance, element: ReactTestInstance): Promise<void> {\n  if (!isHostTextInput(element)) {\n    throw new ErrorWithStack(\n      `clear() only supports host \"TextInput\" elements. Passed element has type: \"${element.type}\".`,\n      clear,\n    );\n  }\n\n  if (!isEditableTextInput(element) || !isPointerEventEnabled(element)) {\n    return;\n  }\n\n  // 1. Enter element\n  dispatchEvent(element, 'focus', EventBuilder.Common.focus());\n\n  // 2. Select all\n  const textToClear = getTextInputValue(element);\n  const selectionRange = {\n    start: 0,\n    end: textToClear.length,\n  };\n  dispatchEvent(element, 'selectionChange', EventBuilder.TextInput.selectionChange(selectionRange));\n\n  // 3. Press backspace with selected text\n  const emptyText = '';\n  await emitTypingEvents(element, {\n    config: this.config,\n    key: 'Backspace',\n    text: emptyText,\n  });\n\n  // 4. Exit element\n  await wait(this.config);\n  dispatchEvent(element, 'endEditing', EventBuilder.TextInput.endEditing(emptyText));\n  dispatchEvent(element, 'blur', EventBuilder.Common.blur());\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AAEA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AAEO,eAAeO,KAAKA,CAA0BC,OAA0B,EAAiB;EAC9F,IAAI,CAAC,IAAAC,mCAAe,EAACD,OAAO,CAAC,EAAE;IAC7B,MAAM,IAAIE,sBAAc,CACtB,8EAA8EF,OAAO,CAACG,IAAI,IAAI,EAC9FJ,KACF,CAAC;EACH;EAEA,IAAI,CAAC,IAAAK,8BAAmB,EAACJ,OAAO,CAAC,IAAI,CAAC,IAAAK,oCAAqB,EAACL,OAAO,CAAC,EAAE;IACpE;EACF;;EAEA;EACA,IAAAM,oBAAa,EAACN,OAAO,EAAE,OAAO,EAAEO,0BAAY,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;;EAE5D;EACA,MAAMC,WAAW,GAAG,IAAAC,4BAAiB,EAACX,OAAO,CAAC;EAC9C,MAAMY,cAAc,GAAG;IACrBC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAEJ,WAAW,CAACK;EACnB,CAAC;EACD,IAAAT,oBAAa,EAACN,OAAO,EAAE,iBAAiB,EAAEO,0BAAY,CAACS,SAAS,CAACC,eAAe,CAACL,cAAc,CAAC,CAAC;;EAEjG;EACA,MAAMM,SAAS,GAAG,EAAE;EACpB,MAAM,IAAAC,sBAAgB,EAACnB,OAAO,EAAE;IAC9BoB,MAAM,EAAE,IAAI,CAACA,MAAM;IACnBC,GAAG,EAAE,WAAW;IAChBC,IAAI,EAAEJ;EACR,CAAC,CAAC;;EAEF;EACA,MAAM,IAAAK,WAAI,EAAC,IAAI,CAACH,MAAM,CAAC;EACvB,IAAAd,oBAAa,EAACN,OAAO,EAAE,YAAY,EAAEO,0BAAY,CAACS,SAAS,CAACQ,UAAU,CAACN,SAAS,CAAC,CAAC;EAClF,IAAAZ,oBAAa,EAACN,OAAO,EAAE,MAAM,EAAEO,0BAAY,CAACC,MAAM,CAACiB,IAAI,CAAC,CAAC,CAAC;AAC5D", "ignoreList": []}