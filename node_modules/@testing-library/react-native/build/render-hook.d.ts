import * as React from 'react';
export type RenderHookResult<Result, Props> = {
    rerender: (props: Props) => void;
    result: React.MutableRefObject<Result>;
    unmount: () => void;
};
export type RenderHookOptions<Props> = {
    /**
     * The initial props to pass to the hook.
     */
    initialProps?: Props;
    /**
     * Pass a React Component as the wrapper option to have it rendered around the inner element. This is most useful for creating
     * reusable custom render functions for common data providers.
     */
    wrapper?: React.ComponentType<any>;
    /**
     * Set to `false` to disable concurrent rendering.
     * Otherwise `renderHook` will default to concurrent rendering.
     */
    concurrentRoot?: boolean;
};
export declare function renderHook<Result, Props>(hookToRender: (props: Props) => Result, options?: RenderHookOptions<Props>): RenderHookResult<Result, Props>;
