{"version": 3, "file": "cleanup.js", "names": ["_screen", "require", "cleanupQueue", "Set", "cleanup", "clearRenderResult", "for<PERSON>ach", "fn", "clear", "addToCleanupQueue", "add"], "sources": ["../src/cleanup.ts"], "sourcesContent": ["import { clearRenderResult } from './screen';\n\ntype CleanUpFunction = () => void;\n\nconst cleanupQueue = new Set<CleanUpFunction>();\n\nexport default function cleanup() {\n  clearRenderResult();\n\n  cleanupQueue.forEach((fn) => fn());\n  cleanupQueue.clear();\n}\n\nexport function addToCleanupQueue(fn: CleanUpFunction) {\n  cleanupQueue.add(fn);\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAIA,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAkB,CAAC;AAEhC,SAASC,OAAOA,CAAA,EAAG;EAChC,IAAAC,yBAAiB,EAAC,CAAC;EAEnBH,YAAY,CAACI,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;EAClCL,YAAY,CAACM,KAAK,CAAC,CAAC;AACtB;AAEO,SAASC,iBAAiBA,CAACF,EAAmB,EAAE;EACrDL,YAAY,CAACQ,GAAG,CAACH,EAAE,CAAC;AACtB", "ignoreList": []}