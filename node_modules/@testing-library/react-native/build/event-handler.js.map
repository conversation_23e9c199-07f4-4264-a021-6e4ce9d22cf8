{"version": 3, "file": "event-handler.js", "names": ["getEventHandler", "element", "eventName", "options", "handler<PERSON>ame", "getEventHandlerName", "props", "loose", "undefined", "capitalizeFirstLetter", "str", "char<PERSON>t", "toUpperCase", "slice"], "sources": ["../src/event-handler.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nexport type EventHandlerOptions = {\n  /** Include check for event handler named without adding `on*` prefix. */\n  loose?: boolean;\n};\n\nexport function getEventHandler(\n  element: ReactTestInstance,\n  eventName: string,\n  options?: EventHandlerOptions,\n) {\n  const handlerName = getEventHandlerName(eventName);\n  if (typeof element.props[handlerName] === 'function') {\n    return element.props[handlerName];\n  }\n\n  if (options?.loose && typeof element.props[eventName] === 'function') {\n    return element.props[eventName];\n  }\n\n  if (typeof element.props[`testOnly_${handlerName}`] === 'function') {\n    return element.props[`testOnly_${handlerName}`];\n  }\n\n  if (options?.loose && typeof element.props[`testOnly_${eventName}`] === 'function') {\n    return element.props[`testOnly_${eventName}`];\n  }\n\n  return undefined;\n}\n\nexport function getEventHandlerName(eventName: string) {\n  return `on${capitalizeFirstLetter(eventName)}`;\n}\n\nfunction capitalizeFirstLetter(str: string) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n"], "mappings": ";;;;;;;AAOO,SAASA,eAAeA,CAC7BC,OAA0B,EAC1BC,SAAiB,EACjBC,OAA6B,EAC7B;EACA,MAAMC,WAAW,GAAGC,mBAAmB,CAACH,SAAS,CAAC;EAClD,IAAI,OAAOD,OAAO,CAACK,KAAK,CAACF,WAAW,CAAC,KAAK,UAAU,EAAE;IACpD,OAAOH,OAAO,CAACK,KAAK,CAACF,WAAW,CAAC;EACnC;EAEA,IAAID,OAAO,EAAEI,KAAK,IAAI,OAAON,OAAO,CAACK,KAAK,CAACJ,SAAS,CAAC,KAAK,UAAU,EAAE;IACpE,OAAOD,OAAO,CAACK,KAAK,CAACJ,SAAS,CAAC;EACjC;EAEA,IAAI,OAAOD,OAAO,CAACK,KAAK,CAAC,YAAYF,WAAW,EAAE,CAAC,KAAK,UAAU,EAAE;IAClE,OAAOH,OAAO,CAACK,KAAK,CAAC,YAAYF,WAAW,EAAE,CAAC;EACjD;EAEA,IAAID,OAAO,EAAEI,KAAK,IAAI,OAAON,OAAO,CAACK,KAAK,CAAC,YAAYJ,SAAS,EAAE,CAAC,KAAK,UAAU,EAAE;IAClF,OAAOD,OAAO,CAACK,KAAK,CAAC,YAAYJ,SAAS,EAAE,CAAC;EAC/C;EAEA,OAAOM,SAAS;AAClB;AAEO,SAASH,mBAAmBA,CAACH,SAAiB,EAAE;EACrD,OAAO,KAAKO,qBAAqB,CAACP,SAAS,CAAC,EAAE;AAChD;AAEA,SAASO,qBAAqBA,CAACC,GAAW,EAAE;EAC1C,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;AACnD", "ignoreList": []}