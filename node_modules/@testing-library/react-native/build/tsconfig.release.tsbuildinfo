{"root": ["../src/act.ts", "../src/cleanup.ts", "../src/config.ts", "../src/event-handler.ts", "../src/fire-event.ts", "../src/flush-micro-tasks.ts", "../src/index.ts", "../src/matches.ts", "../src/native-state.ts", "../src/pure.ts", "../src/react-versions.ts", "../src/render-act.ts", "../src/render-hook.tsx", "../src/render.tsx", "../src/screen.ts", "../src/types.ts", "../src/wait-for-element-to-be-removed.ts", "../src/wait-for.ts", "../src/within.ts", "../src/helpers/accessibility.ts", "../src/helpers/component-tree.ts", "../src/helpers/debug.ts", "../src/helpers/ensure-peer-deps.ts", "../src/helpers/errors.ts", "../src/helpers/find-all.ts", "../src/helpers/format-element.ts", "../src/helpers/host-component-names.ts", "../src/helpers/logger.ts", "../src/helpers/map-props.ts", "../src/helpers/object.ts", "../src/helpers/pointer-events.ts", "../src/helpers/string-validation.ts", "../src/helpers/text-content.ts", "../src/helpers/text-input.ts", "../src/helpers/timers.ts", "../src/helpers/wrap-async.ts", "../src/helpers/matchers/match-accessibility-state.ts", "../src/helpers/matchers/match-accessibility-value.ts", "../src/helpers/matchers/match-array-prop.ts", "../src/helpers/matchers/match-label-text.ts", "../src/helpers/matchers/match-object-prop.ts", "../src/helpers/matchers/match-string-prop.ts", "../src/helpers/matchers/match-text-content.ts", "../src/matchers/extend-expect.ts", "../src/matchers/index.ts", "../src/matchers/to-be-busy.ts", "../src/matchers/to-be-checked.ts", "../src/matchers/to-be-disabled.ts", "../src/matchers/to-be-empty-element.ts", "../src/matchers/to-be-expanded.ts", "../src/matchers/to-be-on-the-screen.ts", "../src/matchers/to-be-partially-checked.ts", "../src/matchers/to-be-selected.ts", "../src/matchers/to-be-visible.ts", "../src/matchers/to-contain-element.ts", "../src/matchers/to-have-accessibility-value.ts", "../src/matchers/to-have-accessible-name.ts", "../src/matchers/to-have-display-value.ts", "../src/matchers/to-have-prop.ts", "../src/matchers/to-have-style.ts", "../src/matchers/to-have-text-content.ts", "../src/matchers/types.ts", "../src/matchers/utils.ts", "../src/queries/display-value.ts", "../src/queries/hint-text.ts", "../src/queries/label-text.ts", "../src/queries/make-queries.ts", "../src/queries/options.ts", "../src/queries/placeholder-text.ts", "../src/queries/role.ts", "../src/queries/test-id.ts", "../src/queries/text.ts", "../src/queries/unsafe-props.ts", "../src/queries/unsafe-type.ts", "../src/test-utils/events.ts", "../src/test-utils/index.ts", "../src/user-event/clear.ts", "../src/user-event/index.ts", "../src/user-event/paste.ts", "../src/user-event/event-builder/base.ts", "../src/user-event/event-builder/common.ts", "../src/user-event/event-builder/index.ts", "../src/user-event/event-builder/scroll-view.ts", "../src/user-event/event-builder/text-input.ts", "../src/user-event/press/index.ts", "../src/user-event/press/press.ts", "../src/user-event/scroll/index.ts", "../src/user-event/scroll/scroll-to.ts", "../src/user-event/scroll/utils.ts", "../src/user-event/setup/index.ts", "../src/user-event/setup/setup.ts", "../src/user-event/type/index.ts", "../src/user-event/type/parse-keys.ts", "../src/user-event/type/type.ts", "../src/user-event/utils/content-size.ts", "../src/user-event/utils/dispatch-event.ts", "../src/user-event/utils/index.ts", "../src/user-event/utils/text-range.ts", "../src/user-event/utils/wait.ts"], "version": "5.7.3"}