"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.renderHook = renderHook;
var React = _interopRequireWildcard(require("react"));
var _render = require("./render");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function renderHook(hookToRender, options) {
  const {
    initialProps,
    ...renderOptions
  } = options ?? {};
  const result = /*#__PURE__*/React.createRef();
  function TestComponent({
    hookProps
  }) {
    const renderResult = hookToRender(hookProps);
    React.useEffect(() => {
      result.current = renderResult;
    });
    return null;
  }
  const {
    rerender: componentRerender,
    unmount
  } = (0, _render.renderInternal)(
  /*#__PURE__*/
  // @ts-expect-error since option can be undefined, initialProps can be undefined when it should'nt
  React.createElement(TestComponent, {
    hookProps: initialProps
  }), renderOptions);
  function rerender(hookProps) {
    return componentRerender(/*#__PURE__*/React.createElement(TestComponent, {
      hookProps: hookProps
    }));
  }
  return {
    // Result should already be set after the first render effects are run.
    result: result,
    rerender,
    unmount
  };
}
//# sourceMappingURL=render-hook.js.map