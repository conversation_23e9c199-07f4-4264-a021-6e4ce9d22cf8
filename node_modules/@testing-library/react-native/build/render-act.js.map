{"version": 3, "file": "render-act.js", "names": ["_reactTest<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_act", "e", "__esModule", "default", "renderWithAct", "component", "options", "renderer", "act", "<PERSON><PERSON><PERSON><PERSON>", "create"], "sources": ["../src/render-act.ts"], "sourcesContent": ["import type { ReactTestRenderer, TestRendererOptions } from 'react-test-renderer';\nimport TestRenderer from 'react-test-renderer';\n\nimport act from './act';\n\nexport function renderWithAct(\n  component: React.ReactElement,\n  options?: Partial<TestRendererOptions>,\n): ReactTestRenderer {\n  let renderer: ReactTestRenderer;\n\n  // This will be called synchronously.\n  void act(() => {\n    // @ts-expect-error `TestRenderer.create` is not typed correctly\n    renderer = TestRenderer.create(component, options);\n  });\n\n  // @ts-expect-error: `act` is synchronous, so `renderer` is already initialized here\n  return renderer;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,kBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,IAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAwB,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEjB,SAASG,aAAaA,CAC3BC,SAA6B,EAC7BC,OAAsC,EACnB;EACnB,IAAIC,QAA2B;;EAE/B;EACA,KAAK,IAAAC,YAAG,EAAC,MAAM;IACb;IACAD,QAAQ,GAAGE,0BAAY,CAACC,MAAM,CAACL,SAAS,EAAEC,OAAO,CAAC;EACpD,CAAC,CAAC;;EAEF;EACA,OAAOC,QAAQ;AACjB", "ignoreList": []}