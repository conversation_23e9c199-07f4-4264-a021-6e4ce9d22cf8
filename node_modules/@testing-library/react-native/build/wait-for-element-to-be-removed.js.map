{"version": 3, "file": "wait-for-element-to-be-removed.js", "names": ["_errors", "require", "_waitFor", "_interopRequireDefault", "e", "__esModule", "default", "isRemoved", "result", "Array", "isArray", "length", "waitForElementToBeRemoved", "expectation", "options", "timeoutError", "ErrorWithStack", "initialElements", "waitFor"], "sources": ["../src/wait-for-element-to-be-removed.ts"], "sourcesContent": ["import { ErrorWithStack } from './helpers/errors';\nimport type { WaitForOptions } from './wait-for';\nimport waitFor from './wait-for';\n\nfunction isRemoved<T>(result: T): boolean {\n  return !result || (Array.isArray(result) && !result.length);\n}\n\nexport default async function waitForElementToBeRemoved<T>(\n  expectation: () => T,\n  options?: WaitForOptions,\n): Promise<T> {\n  // Created here so we get a nice stacktrace\n  const timeoutError = new ErrorWithStack(\n    'Timed out in waitForElementToBeRemoved.',\n    waitForElementToBeRemoved,\n  );\n\n  // Elements have to be present initally and then removed.\n  const initialElements = expectation();\n  if (isRemoved(initialElements)) {\n    throw new ErrorWithStack(\n      'The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.',\n      waitForElementToBeRemoved,\n    );\n  }\n\n  return await waitFor(() => {\n    let result;\n    try {\n      result = expectation();\n    } catch {\n      return initialElements;\n    }\n\n    if (!isRemoved(result)) {\n      throw timeoutError;\n    }\n\n    return initialElements;\n  }, options);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAiC,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEjC,SAASG,SAASA,CAAIC,MAAS,EAAW;EACxC,OAAO,CAACA,MAAM,IAAKC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAI,CAACA,MAAM,CAACG,MAAO;AAC7D;AAEe,eAAeC,yBAAyBA,CACrDC,WAAoB,EACpBC,OAAwB,EACZ;EACZ;EACA,MAAMC,YAAY,GAAG,IAAIC,sBAAc,CACrC,yCAAyC,EACzCJ,yBACF,CAAC;;EAED;EACA,MAAMK,eAAe,GAAGJ,WAAW,CAAC,CAAC;EACrC,IAAIN,SAAS,CAACU,eAAe,CAAC,EAAE;IAC9B,MAAM,IAAID,sBAAc,CACtB,oKAAoK,EACpKJ,yBACF,CAAC;EACH;EAEA,OAAO,MAAM,IAAAM,gBAAO,EAAC,MAAM;IACzB,IAAIV,MAAM;IACV,IAAI;MACFA,MAAM,GAAGK,WAAW,CAAC,CAAC;IACxB,CAAC,CAAC,MAAM;MACN,OAAOI,eAAe;IACxB;IAEA,IAAI,CAACV,SAAS,CAACC,MAAM,CAAC,EAAE;MACtB,MAAMO,YAAY;IACpB;IAEA,OAAOE,eAAe;EACxB,CAAC,EAAEH,OAAO,CAAC;AACb", "ignoreList": []}