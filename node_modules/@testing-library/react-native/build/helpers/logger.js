"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.logger = void 0;
var _chalk = _interopRequireDefault(require("chalk"));
var nodeConsole = _interopRequireWildcard(require("console"));
var _redent = _interopRequireDefault(require("redent"));
var nodeUtil = _interopRequireWildcard(require("util"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const logger = exports.logger = {
  debug(message, ...args) {
    const output = formatMessage('●', message, ...args);
    nodeConsole.debug(_chalk.default.dim(output));
  },
  info(message, ...args) {
    const output = formatMessage('●', message, ...args);
    nodeConsole.info(output);
  },
  warn(message, ...args) {
    const output = formatMessage('▲', message, ...args);
    nodeConsole.warn(_chalk.default.yellow(output));
  },
  error(message, ...args) {
    const output = formatMessage('■', message, ...args);
    nodeConsole.error(_chalk.default.red(output));
  }
};
function formatMessage(symbol, message, ...args) {
  const formatted = nodeUtil.format(message, ...args);
  const indented = (0, _redent.default)(formatted, 4);
  return `  ${symbol} ${indented.trimStart()}\n`;
}
//# sourceMappingURL=logger.js.map