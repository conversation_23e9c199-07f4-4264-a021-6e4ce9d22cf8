{"version": 3, "file": "logger.js", "names": ["_chalk", "_interopRequireDefault", "require", "nodeConsole", "_interopRequireWildcard", "_redent", "nodeUtil", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "logger", "exports", "debug", "message", "args", "output", "formatMessage", "chalk", "dim", "info", "warn", "yellow", "error", "red", "symbol", "formatted", "format", "indented", "redent", "trimStart"], "sources": ["../../src/helpers/logger.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport * as nodeConsole from 'console';\nimport redent from 'redent';\nimport * as nodeUtil from 'util';\n\nexport const logger = {\n  debug(message: unknown, ...args: unknown[]) {\n    const output = formatMessage('●', message, ...args);\n    nodeConsole.debug(chalk.dim(output));\n  },\n\n  info(message: unknown, ...args: unknown[]) {\n    const output = formatMessage('●', message, ...args);\n    nodeConsole.info(output);\n  },\n\n  warn(message: unknown, ...args: unknown[]) {\n    const output = formatMessage('▲', message, ...args);\n    nodeConsole.warn(chalk.yellow(output));\n  },\n\n  error(message: unknown, ...args: unknown[]) {\n    const output = formatMessage('■', message, ...args);\n    nodeConsole.error(chalk.red(output));\n  },\n};\n\nfunction formatMessage(symbol: string, message: unknown, ...args: unknown[]) {\n  const formatted = nodeUtil.format(message, ...args);\n  const indented = redent(formatted, 4);\n  return `  ${symbol} ${indented.trimStart()}\\n`;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,QAAA,GAAAF,uBAAA,CAAAF,OAAA;AAAiC,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAf,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAE1B,MAAMmB,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG;EACpBE,KAAKA,CAACC,OAAgB,EAAE,GAAGC,IAAe,EAAE;IAC1C,MAAMC,MAAM,GAAGC,aAAa,CAAC,GAAG,EAAEH,OAAO,EAAE,GAAGC,IAAI,CAAC;IACnD5B,WAAW,CAAC0B,KAAK,CAACK,cAAK,CAACC,GAAG,CAACH,MAAM,CAAC,CAAC;EACtC,CAAC;EAEDI,IAAIA,CAACN,OAAgB,EAAE,GAAGC,IAAe,EAAE;IACzC,MAAMC,MAAM,GAAGC,aAAa,CAAC,GAAG,EAAEH,OAAO,EAAE,GAAGC,IAAI,CAAC;IACnD5B,WAAW,CAACiC,IAAI,CAACJ,MAAM,CAAC;EAC1B,CAAC;EAEDK,IAAIA,CAACP,OAAgB,EAAE,GAAGC,IAAe,EAAE;IACzC,MAAMC,MAAM,GAAGC,aAAa,CAAC,GAAG,EAAEH,OAAO,EAAE,GAAGC,IAAI,CAAC;IACnD5B,WAAW,CAACkC,IAAI,CAACH,cAAK,CAACI,MAAM,CAACN,MAAM,CAAC,CAAC;EACxC,CAAC;EAEDO,KAAKA,CAACT,OAAgB,EAAE,GAAGC,IAAe,EAAE;IAC1C,MAAMC,MAAM,GAAGC,aAAa,CAAC,GAAG,EAAEH,OAAO,EAAE,GAAGC,IAAI,CAAC;IACnD5B,WAAW,CAACoC,KAAK,CAACL,cAAK,CAACM,GAAG,CAACR,MAAM,CAAC,CAAC;EACtC;AACF,CAAC;AAED,SAASC,aAAaA,CAACQ,MAAc,EAAEX,OAAgB,EAAE,GAAGC,IAAe,EAAE;EAC3E,MAAMW,SAAS,GAAGpC,QAAQ,CAACqC,MAAM,CAACb,OAAO,EAAE,GAAGC,IAAI,CAAC;EACnD,MAAMa,QAAQ,GAAG,IAAAC,eAAM,EAACH,SAAS,EAAE,CAAC,CAAC;EACrC,OAAO,KAAKD,MAAM,IAAIG,QAAQ,CAACE,SAAS,CAAC,CAAC,IAAI;AAChD", "ignoreList": []}