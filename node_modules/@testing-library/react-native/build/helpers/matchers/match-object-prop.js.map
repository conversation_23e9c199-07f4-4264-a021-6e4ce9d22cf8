{"version": 3, "file": "match-object-prop.js", "names": ["matchObjectProp", "prop", "matcher", "Object", "keys", "length", "every", "key"], "sources": ["../../../src/helpers/matchers/match-object-prop.ts"], "sourcesContent": ["/**\n * check that each key value pair of the objects match\n * BE CAREFUL it works only for 1 level deep key value pairs\n * won't work for nested objects\n */\n\n/**\n * Matches whether given object prop contains all key/value pairs.\n * @param prop - The object prop to match.\n * @param matcher - The key/value pairs to be included in the object.\n * @returns Whether the object prop contains all key/value pairs.\n */\nexport function matchObjectProp<T extends Record<string, unknown>>(\n  prop: T | undefined,\n  matcher: T,\n): boolean {\n  if (!prop || Object.keys(matcher).length === 0) {\n    return false;\n  }\n\n  return (\n    Object.keys(prop).length !== 0 &&\n    Object.keys(matcher).every((key) => prop[key] === matcher[key])\n  );\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,eAAeA,CAC7BC,IAAmB,EACnBC,OAAU,EACD;EACT,IAAI,CAACD,IAAI,IAAIE,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IAC9C,OAAO,KAAK;EACd;EAEA,OACEF,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,MAAM,KAAK,CAAC,IAC9BF,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACI,KAAK,CAAEC,GAAG,IAAKN,IAAI,CAACM,GAAG,CAAC,KAAKL,OAAO,CAACK,GAAG,CAAC,CAAC;AAEnE", "ignoreList": []}