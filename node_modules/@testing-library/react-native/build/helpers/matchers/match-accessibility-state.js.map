{"version": 3, "file": "match-accessibility-state.js", "names": ["_accessibility", "require", "matchAccessibilityState", "node", "matcher", "busy", "undefined", "computeAriaBusy", "checked", "computeAriaChecked", "disabled", "computeAriaDisabled", "expanded", "computeAriaExpanded", "selected", "computeAriaSelected"], "sources": ["../../../src/helpers/matchers/match-accessibility-state.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport {\n  computeAriaBusy,\n  computeAriaChecked,\n  computeAriaDisabled,\n  computeAriaExpanded,\n  computeAriaSelected,\n} from '../accessibility';\n\n// This type is the same as AccessibilityState from `react-native` package\n// It is re-declared here due to issues with migration from `@types/react-native` to\n// built in `react-native` types.\n// See: https://github.com/callstack/react-native-testing-library/issues/1351\nexport interface AccessibilityStateMatcher {\n  disabled?: boolean;\n  selected?: boolean;\n  checked?: boolean | 'mixed';\n  busy?: boolean;\n  expanded?: boolean;\n}\n\nexport function matchAccessibilityState(\n  node: ReactTestInstance,\n  matcher: AccessibilityStateMatcher,\n) {\n  if (matcher.busy !== undefined && matcher.busy !== computeAriaBusy(node)) {\n    return false;\n  }\n  if (matcher.checked !== undefined && matcher.checked !== computeAriaChecked(node)) {\n    return false;\n  }\n  if (matcher.disabled !== undefined && matcher.disabled !== computeAriaDisabled(node)) {\n    return false;\n  }\n  if (matcher.expanded !== undefined && matcher.expanded !== computeAriaExpanded(node)) {\n    return false;\n  }\n  if (matcher.selected !== undefined && matcher.selected !== computeAriaSelected(node)) {\n    return false;\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAQA;AACA;AACA;AACA;;AASO,SAASC,uBAAuBA,CACrCC,IAAuB,EACvBC,OAAkC,EAClC;EACA,IAAIA,OAAO,CAACC,IAAI,KAAKC,SAAS,IAAIF,OAAO,CAACC,IAAI,KAAK,IAAAE,8BAAe,EAACJ,IAAI,CAAC,EAAE;IACxE,OAAO,KAAK;EACd;EACA,IAAIC,OAAO,CAACI,OAAO,KAAKF,SAAS,IAAIF,OAAO,CAACI,OAAO,KAAK,IAAAC,iCAAkB,EAACN,IAAI,CAAC,EAAE;IACjF,OAAO,KAAK;EACd;EACA,IAAIC,OAAO,CAACM,QAAQ,KAAKJ,SAAS,IAAIF,OAAO,CAACM,QAAQ,KAAK,IAAAC,kCAAmB,EAACR,IAAI,CAAC,EAAE;IACpF,OAAO,KAAK;EACd;EACA,IAAIC,OAAO,CAACQ,QAAQ,KAAKN,SAAS,IAAIF,OAAO,CAACQ,QAAQ,KAAK,IAAAC,kCAAmB,EAACV,IAAI,CAAC,EAAE;IACpF,OAAO,KAAK;EACd;EACA,IAAIC,OAAO,CAACU,QAAQ,KAAKR,SAAS,IAAIF,OAAO,CAACU,QAAQ,KAAK,IAAAC,kCAAmB,EAACZ,IAAI,CAAC,EAAE;IACpF,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb", "ignoreList": []}