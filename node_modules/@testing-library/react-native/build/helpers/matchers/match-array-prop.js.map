{"version": 3, "file": "match-array-prop.js", "names": ["matchArrayProp", "prop", "matcher", "length", "includes", "every", "e"], "sources": ["../../../src/helpers/matchers/match-array-prop.ts"], "sourcesContent": ["/**\n * Matches whether given array prop contains the given value, or all given values.\n *\n * @param prop - The array prop to match.\n * @param matcher - The value or values to be included in the array.\n * @returns Whether the array prop contains the given value, or all given values.\n */\nexport function matchArrayProp(\n  prop: Array<string> | undefined,\n  matcher: string | Array<string>,\n): boolean {\n  if (!prop || matcher.length === 0) {\n    return false;\n  }\n\n  if (typeof matcher === 'string') {\n    return prop.includes(matcher);\n  }\n\n  return matcher.every((e) => prop.includes(e));\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,cAAcA,CAC5BC,IAA+B,EAC/BC,OAA+B,EACtB;EACT,IAAI,CAACD,IAAI,IAAIC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOD,IAAI,CAACG,QAAQ,CAACF,OAAO,CAAC;EAC/B;EAEA,OAAOA,OAAO,CAACG,KAAK,CAAEC,CAAC,IAAKL,IAAI,CAACG,QAAQ,CAACE,CAAC,CAAC,CAAC;AAC/C", "ignoreList": []}