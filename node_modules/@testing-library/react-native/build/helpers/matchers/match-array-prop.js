"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchArrayProp = matchArrayProp;
/**
 * Matches whether given array prop contains the given value, or all given values.
 *
 * @param prop - The array prop to match.
 * @param matcher - The value or values to be included in the array.
 * @returns Whether the array prop contains the given value, or all given values.
 */
function matchArrayProp(prop, matcher) {
  if (!prop || matcher.length === 0) {
    return false;
  }
  if (typeof matcher === 'string') {
    return prop.includes(matcher);
  }
  return matcher.every(e => prop.includes(e));
}
//# sourceMappingURL=match-array-prop.js.map