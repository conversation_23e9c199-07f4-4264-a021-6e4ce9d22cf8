{"version": 3, "file": "match-label-text.js", "names": ["_matches", "require", "_accessibility", "matchAccessibilityLabel", "element", "<PERSON><PERSON><PERSON><PERSON>", "options", "matches", "computeAriaLabel", "normalizer", "exact"], "sources": ["../../../src/helpers/matchers/match-label-text.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport type { TextMatch, TextMatchOptions } from '../../matches';\nimport { matches } from '../../matches';\nimport { computeAriaLabel } from '../accessibility';\n\nexport function matchAccessibilityLabel(\n  element: ReactTestInstance,\n  expectedLabel: TextMatch,\n  options?: TextMatchOptions,\n) {\n  return matches(expectedLabel, computeAriaLabel(element), options?.normalizer, options?.exact);\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAEO,SAASE,uBAAuBA,CACrCC,OAA0B,EAC1BC,aAAwB,EACxBC,OAA0B,EAC1B;EACA,OAAO,IAAAC,gBAAO,EAACF,aAAa,EAAE,IAAAG,+BAAgB,EAACJ,OAAO,CAAC,EAAEE,OAAO,EAAEG,UAAU,EAAEH,OAAO,EAAEI,KAAK,CAAC;AAC/F", "ignoreList": []}