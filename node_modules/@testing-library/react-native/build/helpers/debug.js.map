{"version": 3, "file": "debug.js", "names": ["_formatElement", "require", "_logger", "debug", "instance", "message", "formatOptions", "logger", "info", "formatJson"], "sources": ["../../src/helpers/debug.ts"], "sourcesContent": ["import type { ReactTestRendererJSO<PERSON> } from 'react-test-renderer';\n\nimport type { FormatElementOptions } from './format-element';\nimport { formatJson } from './format-element';\nimport { logger } from './logger';\n\nexport type DebugOptions = {\n  message?: string;\n} & FormatElementOptions;\n\n/**\n * Log pretty-printed deep test component instance\n */\nexport function debug(\n  instance: ReactTestRendererJSON | ReactTestRendererJSON[],\n  { message, ...formatOptions }: DebugOptions = {},\n) {\n  if (message) {\n    logger.info(`${message}\\n\\n`, formatJson(instance, formatOptions));\n  } else {\n    logger.info(formatJson(instance, formatOptions));\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAMA;AACA;AACA;AACO,SAASE,KAAKA,CACnBC,QAAyD,EACzD;EAAEC,OAAO;EAAE,GAAGC;AAA4B,CAAC,GAAG,CAAC,CAAC,EAChD;EACA,IAAID,OAAO,EAAE;IACXE,cAAM,CAACC,IAAI,CAAC,GAAGH,OAAO,MAAM,EAAE,IAAAI,yBAAU,EAACL,QAAQ,EAAEE,aAAa,CAAC,CAAC;EACpE,CAAC,MAAM;IACLC,cAAM,CAACC,IAAI,CAAC,IAAAC,yBAAU,EAACL,QAAQ,EAAEE,aAAa,CAAC,CAAC;EAClD;AACF", "ignoreList": []}