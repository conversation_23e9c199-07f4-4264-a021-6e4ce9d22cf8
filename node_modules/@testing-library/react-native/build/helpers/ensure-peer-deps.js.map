{"version": 3, "file": "ensure-peer-deps.js", "names": ["ensurePeerDeps", "reactVersion", "getPackageVersion", "ensurePackage", "name", "expectedVersion", "actualVersion", "error", "Error", "captureStackTrace", "packageJson", "require", "version", "process", "env", "RNTL_SKIP_DEPS_CHECK"], "sources": ["../../src/helpers/ensure-peer-deps.ts"], "sourcesContent": ["function ensurePeerDeps() {\n  const reactVersion = getPackageVersion('react');\n  ensurePackage('react-test-renderer', reactVersion);\n}\n\nfunction ensurePackage(name: string, expectedVersion: string) {\n  const actualVersion = getPackageVersion(name);\n  if (!actualVersion) {\n    const error = new Error(\n      `Missing dev dependency \"${name}@${expectedVersion}\".\\n\\nFix it by running:\\nnpm install -D ${name}@${expectedVersion}`,\n    );\n    Error.captureStackTrace(error, ensurePeerDeps);\n    throw error;\n  }\n\n  if (expectedVersion !== actualVersion) {\n    const error = new Error(\n      `Incorrect version of \"${name}\" detected. Expected \"${expectedVersion}\", but found \"${actualVersion}\".\\n\\nFix it by running:\\nnpm install -D ${name}@${expectedVersion}`,\n    );\n    Error.captureStackTrace(error, ensurePeerDeps);\n    throw error;\n  }\n}\n\nfunction getPackageVersion(name: string) {\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const packageJson = require(`${name}/package.json`);\n    return packageJson.version;\n  } catch {\n    return null;\n  }\n}\n\nif (!process.env.RNTL_SKIP_DEPS_CHECK) {\n  ensurePeerDeps();\n}\n"], "mappings": ";;AAAA,SAASA,cAAcA,CAAA,EAAG;EACxB,MAAMC,YAAY,GAAGC,iBAAiB,CAAC,OAAO,CAAC;EAC/CC,aAAa,CAAC,qBAAqB,EAAEF,YAAY,CAAC;AACpD;AAEA,SAASE,aAAaA,CAACC,IAAY,EAAEC,eAAuB,EAAE;EAC5D,MAAMC,aAAa,GAAGJ,iBAAiB,CAACE,IAAI,CAAC;EAC7C,IAAI,CAACE,aAAa,EAAE;IAClB,MAAMC,KAAK,GAAG,IAAIC,KAAK,CACrB,2BAA2BJ,IAAI,IAAIC,eAAe,4CAA4CD,IAAI,IAAIC,eAAe,EACvH,CAAC;IACDG,KAAK,CAACC,iBAAiB,CAACF,KAAK,EAAEP,cAAc,CAAC;IAC9C,MAAMO,KAAK;EACb;EAEA,IAAIF,eAAe,KAAKC,aAAa,EAAE;IACrC,MAAMC,KAAK,GAAG,IAAIC,KAAK,CACrB,yBAAyBJ,IAAI,yBAAyBC,eAAe,iBAAiBC,aAAa,4CAA4CF,IAAI,IAAIC,eAAe,EACxK,CAAC;IACDG,KAAK,CAACC,iBAAiB,CAACF,KAAK,EAAEP,cAAc,CAAC;IAC9C,MAAMO,KAAK;EACb;AACF;AAEA,SAASL,iBAAiBA,CAACE,IAAY,EAAE;EACvC,IAAI;IACF;IACA,MAAMM,WAAW,GAAGC,OAAO,CAAC,GAAGP,IAAI,eAAe,CAAC;IACnD,OAAOM,WAAW,CAACE,OAAO;EAC5B,CAAC,CAAC,MAAM;IACN,OAAO,IAAI;EACb;AACF;AAEA,IAAI,CAACC,OAAO,CAACC,GAAG,CAACC,oBAAoB,EAAE;EACrCf,cAAc,CAAC,CAAC;AAClB", "ignoreList": []}