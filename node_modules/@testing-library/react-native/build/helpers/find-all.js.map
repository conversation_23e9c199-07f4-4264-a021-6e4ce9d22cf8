{"version": 3, "file": "find-all.js", "names": ["_config", "require", "_accessibility", "_componentTree", "findAll", "root", "predicate", "options", "results", "findAllInternal", "includeHiddenElements", "hidden", "getConfig", "defaultIncludeHiddenElements", "cache", "WeakMap", "filter", "element", "isHiddenFromAccessibility", "matchingDescendants", "children", "for<PERSON>ach", "child", "push", "matchDeepestOnly", "length", "isHostElement"], "sources": ["../../src/helpers/find-all.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { getConfig } from '../config';\nimport { isHiddenFromAccessibility } from './accessibility';\nimport type { HostTestInstance } from './component-tree';\nimport { isHostElement } from './component-tree';\n\ninterface FindAllOptions {\n  /** Match elements hidden from accessibility */\n  includeHiddenElements?: boolean;\n\n  /** RTL-compatible alias to `includeHiddenElements` */\n  hidden?: boolean;\n\n  /* Exclude any ancestors of deepest matched elements even if they match the predicate */\n  matchDeepestOnly?: boolean;\n}\n\nexport function findAll(\n  root: ReactTestInstance,\n  predicate: (element: ReactTestInstance) => boolean,\n  options?: FindAllOptions,\n): HostTestInstance[] {\n  const results = findAllInternal(root, predicate, options);\n\n  const includeHiddenElements =\n    options?.includeHiddenElements ?? options?.hidden ?? getConfig()?.defaultIncludeHiddenElements;\n\n  if (includeHiddenElements) {\n    return results;\n  }\n\n  const cache = new WeakMap<ReactTestInstance>();\n  return results.filter((element) => !isHiddenFromAccessibility(element, { cache }));\n}\n\n// Extracted from React Test Renderer\n// src: https://github.com/facebook/react/blob/8e2bde6f2751aa6335f3cef488c05c3ea08e074a/packages/react-test-renderer/src/ReactTestRenderer.js#L402\nfunction findAllInternal(\n  root: ReactTestInstance,\n  predicate: (element: ReactTestInstance) => boolean,\n  options?: FindAllOptions,\n): HostTestInstance[] {\n  const results: HostTestInstance[] = [];\n\n  // Match descendants first but do not add them to results yet.\n  const matchingDescendants: HostTestInstance[] = [];\n  root.children.forEach((child) => {\n    if (typeof child === 'string') {\n      return;\n    }\n    matchingDescendants.push(...findAllInternal(child, predicate, options));\n  });\n\n  if (\n    // When matchDeepestOnly = true: add current element only if no descendants match\n    (!options?.matchDeepestOnly || matchingDescendants.length === 0) &&\n    isHostElement(root) &&\n    predicate(root)\n  ) {\n    results.push(root);\n  }\n\n  // Add matching descendants after element to preserve original tree walk order.\n  results.push(...matchingDescendants);\n\n  return results;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAEA,IAAAE,cAAA,GAAAF,OAAA;AAaO,SAASG,OAAOA,CACrBC,IAAuB,EACvBC,SAAkD,EAClDC,OAAwB,EACJ;EACpB,MAAMC,OAAO,GAAGC,eAAe,CAACJ,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAEzD,MAAMG,qBAAqB,GACzBH,OAAO,EAAEG,qBAAqB,IAAIH,OAAO,EAAEI,MAAM,IAAI,IAAAC,iBAAS,EAAC,CAAC,EAAEC,4BAA4B;EAEhG,IAAIH,qBAAqB,EAAE;IACzB,OAAOF,OAAO;EAChB;EAEA,MAAMM,KAAK,GAAG,IAAIC,OAAO,CAAoB,CAAC;EAC9C,OAAOP,OAAO,CAACQ,MAAM,CAAEC,OAAO,IAAK,CAAC,IAAAC,wCAAyB,EAACD,OAAO,EAAE;IAAEH;EAAM,CAAC,CAAC,CAAC;AACpF;;AAEA;AACA;AACA,SAASL,eAAeA,CACtBJ,IAAuB,EACvBC,SAAkD,EAClDC,OAAwB,EACJ;EACpB,MAAMC,OAA2B,GAAG,EAAE;;EAEtC;EACA,MAAMW,mBAAuC,GAAG,EAAE;EAClDd,IAAI,CAACe,QAAQ,CAACC,OAAO,CAAEC,KAAK,IAAK;IAC/B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B;IACF;IACAH,mBAAmB,CAACI,IAAI,CAAC,GAAGd,eAAe,CAACa,KAAK,EAAEhB,SAAS,EAAEC,OAAO,CAAC,CAAC;EACzE,CAAC,CAAC;EAEF;EACE;EACA,CAAC,CAACA,OAAO,EAAEiB,gBAAgB,IAAIL,mBAAmB,CAACM,MAAM,KAAK,CAAC,KAC/D,IAAAC,4BAAa,EAACrB,IAAI,CAAC,IACnBC,SAAS,CAACD,IAAI,CAAC,EACf;IACAG,OAAO,CAACe,IAAI,CAAClB,IAAI,CAAC;EACpB;;EAEA;EACAG,OAAO,CAACe,IAAI,CAAC,GAAGJ,mBAAmB,CAAC;EAEpC,OAAOX,OAAO;AAChB", "ignoreList": []}