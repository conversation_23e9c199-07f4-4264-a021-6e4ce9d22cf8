"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.formatElement = formatElement;
exports.formatElementList = formatElementList;
exports.formatJson = formatJson;
var _prettyFormat = _interopRequireWildcard(require("pretty-format"));
var _mapProps = require("./map-props");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/***
 * Format given element as a pretty-printed string.
 *
 * @param element Element to format.
 */
function formatElement(element, {
  compact,
  highlight = true,
  mapProps = _mapProps.defaultMapProps
} = {}) {
  if (element == null) {
    return '(null)';
  }
  const {
    children,
    ...props
  } = element.props;
  const childrenToDisplay = typeof children === 'string' ? [children] : undefined;
  return (0, _prettyFormat.default)({
    // This prop is needed persuade the prettyFormat that the element is
    // a ReactTestRendererJSON instance, so it is formatted as JSX.
    $$typeof: Symbol.for('react.test.json'),
    type: `${element.type}`,
    props: mapProps ? mapProps(props) : props,
    children: childrenToDisplay
  },
  // See: https://www.npmjs.com/package/pretty-format#usage-with-options
  {
    plugins: [_prettyFormat.plugins.ReactTestComponent, _prettyFormat.plugins.ReactElement],
    printFunctionName: false,
    printBasicPrototype: false,
    highlight: highlight,
    min: compact
  });
}
function formatElementList(elements, options) {
  if (elements.length === 0) {
    return '(no elements)';
  }
  return elements.map(element => formatElement(element, options)).join('\n');
}
function formatJson(json, {
  compact,
  highlight = true,
  mapProps = _mapProps.defaultMapProps
} = {}) {
  return (0, _prettyFormat.default)(json, {
    plugins: [getElementJsonPlugin(mapProps), _prettyFormat.plugins.ReactElement],
    highlight: highlight,
    printBasicPrototype: false,
    min: compact
  });
}
function getElementJsonPlugin(mapProps) {
  return {
    test: val => _prettyFormat.plugins.ReactTestComponent.test(val),
    serialize: (val, config, indentation, depth, refs, printer) => {
      let newVal = val;
      if (mapProps && val.props) {
        newVal = {
          ...val,
          props: mapProps(val.props)
        };
      }
      return _prettyFormat.plugins.ReactTestComponent.serialize(newVal, config, indentation, depth, refs, printer);
    }
  };
}
//# sourceMappingURL=format-element.js.map