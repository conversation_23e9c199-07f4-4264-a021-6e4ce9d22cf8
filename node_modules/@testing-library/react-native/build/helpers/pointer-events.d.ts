import type { ReactTestInstance } from 'react-test-renderer';
/**
 * pointerEvents controls whether the View can be the target of touch events.
 * 'auto': The View and its children can be the target of touch events.
 * 'none': The View is never the target of touch events.
 * 'box-none': The View is never the target of touch events but its subviews can be
 * 'box-only': The view can be the target of touch events but its subviews cannot be
 * see the official react native doc https://reactnative.dev/docs/view#pointerevents */
export declare const isPointerEventEnabled: (element: ReactTestInstance, isParent?: boolean) => boolean;
