{"version": 3, "file": "component-tree.js", "names": ["_screen", "require", "isHostElement", "element", "type", "isElementMounted", "getUnsafeRootElement", "screen", "UNSAFE_root", "getHostParent", "current", "parent", "getHostChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "for<PERSON>ach", "child", "push", "getHostSelves", "getHostSiblings", "hostParent", "hostSelves", "filter", "sibling", "includes"], "sources": ["../../src/helpers/component-tree.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { screen } from '../screen';\n/**\n * ReactTestInstance referring to host element.\n */\nexport type HostTestInstance = ReactTestInstance & { type: string };\n\n/**\n * Checks if the given element is a host element.\n * @param element The element to check.\n */\nexport function isHostElement(element?: ReactTestInstance | null): element is HostTestInstance {\n  return typeof element?.type === 'string';\n}\n\nexport function isElementMounted(element: ReactTestInstance) {\n  return getUnsafeRootElement(element) === screen.UNSAFE_root;\n}\n\n/**\n * Returns first host ancestor for given element.\n * @param element The element start traversing from.\n */\nexport function getHostParent(element: ReactTestInstance | null): HostTestInstance | null {\n  if (element == null) {\n    return null;\n  }\n\n  let current = element.parent;\n  while (current) {\n    if (isHostElement(current)) {\n      return current;\n    }\n\n    current = current.parent;\n  }\n\n  return null;\n}\n\n/**\n * Returns host children for given element.\n * @param element The element start traversing from.\n */\nexport function getHostChildren(element: ReactTestInstance | null): HostTestInstance[] {\n  if (element == null) {\n    return [];\n  }\n\n  const hostChildren: HostTestInstance[] = [];\n\n  element.children.forEach((child) => {\n    if (typeof child !== 'object') {\n      return;\n    }\n\n    if (isHostElement(child)) {\n      hostChildren.push(child);\n    } else {\n      hostChildren.push(...getHostChildren(child));\n    }\n  });\n\n  return hostChildren;\n}\n\n/**\n * Return the array of host elements that represent the passed element.\n *\n * @param element The element start traversing from.\n * @returns If the passed element is a host element, it will return an array containing only that element,\n * if the passed element is a composite element, it will return an array containing its host children (zero, one or many).\n */\nexport function getHostSelves(element: ReactTestInstance | null): HostTestInstance[] {\n  return isHostElement(element) ? [element] : getHostChildren(element);\n}\n\n/**\n * Returns host siblings for given element.\n * @param element The element start traversing from.\n */\nexport function getHostSiblings(element: ReactTestInstance | null): HostTestInstance[] {\n  const hostParent = getHostParent(element);\n  const hostSelves = getHostSelves(element);\n  return getHostChildren(hostParent).filter((sibling) => !hostSelves.includes(sibling));\n}\n\n/**\n * Returns the unsafe root element of the tree (probably composite).\n *\n * @param element The element start traversing from.\n * @returns The root element of the tree (host or composite).\n */\nexport function getUnsafeRootElement(element: ReactTestInstance) {\n  let current = element;\n  while (current.parent) {\n    current = current.parent;\n  }\n\n  return current;\n}\n"], "mappings": ";;;;;;;;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAACC,OAAkC,EAA+B;EAC7F,OAAO,OAAOA,OAAO,EAAEC,IAAI,KAAK,QAAQ;AAC1C;AAEO,SAASC,gBAAgBA,CAACF,OAA0B,EAAE;EAC3D,OAAOG,oBAAoB,CAACH,OAAO,CAAC,KAAKI,cAAM,CAACC,WAAW;AAC7D;;AAEA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAACN,OAAiC,EAA2B;EACxF,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIO,OAAO,GAAGP,OAAO,CAACQ,MAAM;EAC5B,OAAOD,OAAO,EAAE;IACd,IAAIR,aAAa,CAACQ,OAAO,CAAC,EAAE;MAC1B,OAAOA,OAAO;IAChB;IAEAA,OAAO,GAAGA,OAAO,CAACC,MAAM;EAC1B;EAEA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACO,SAASC,eAAeA,CAACT,OAAiC,EAAsB;EACrF,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,EAAE;EACX;EAEA,MAAMU,YAAgC,GAAG,EAAE;EAE3CV,OAAO,CAACW,QAAQ,CAACC,OAAO,CAAEC,KAAK,IAAK;IAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B;IACF;IAEA,IAAId,aAAa,CAACc,KAAK,CAAC,EAAE;MACxBH,YAAY,CAACI,IAAI,CAACD,KAAK,CAAC;IAC1B,CAAC,MAAM;MACLH,YAAY,CAACI,IAAI,CAAC,GAAGL,eAAe,CAACI,KAAK,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC;EAEF,OAAOH,YAAY;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,aAAaA,CAACf,OAAiC,EAAsB;EACnF,OAAOD,aAAa,CAACC,OAAO,CAAC,GAAG,CAACA,OAAO,CAAC,GAAGS,eAAe,CAACT,OAAO,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACO,SAASgB,eAAeA,CAAChB,OAAiC,EAAsB;EACrF,MAAMiB,UAAU,GAAGX,aAAa,CAACN,OAAO,CAAC;EACzC,MAAMkB,UAAU,GAAGH,aAAa,CAACf,OAAO,CAAC;EACzC,OAAOS,eAAe,CAACQ,UAAU,CAAC,CAACE,MAAM,CAAEC,OAAO,IAAK,CAACF,UAAU,CAACG,QAAQ,CAACD,OAAO,CAAC,CAAC;AACvF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASjB,oBAAoBA,CAACH,OAA0B,EAAE;EAC/D,IAAIO,OAAO,GAAGP,OAAO;EACrB,OAAOO,OAAO,CAACC,MAAM,EAAE;IACrBD,OAAO,GAAGA,OAAO,CAACC,MAAM;EAC1B;EAEA,OAAOD,OAAO;AAChB", "ignoreList": []}