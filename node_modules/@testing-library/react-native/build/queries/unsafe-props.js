"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.bindUnsafeByPropsQueries = void 0;
var _prettyFormat = _interopRequireDefault(require("pretty-format"));
var _errors = require("../helpers/errors");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const UNSAFE_getByProps = instance => function getByPropsFn(props) {
  try {
    return instance.findByProps(props);
  } catch (error) {
    throw new _errors.ErrorWithStack((0, _errors.prepareErrorMessage)(error), getByPropsFn);
  }
};
const UNSAFE_getAllByProps = instance => function getAllByPropsFn(props) {
  const results = instance.findAllByProps(props);
  if (results.length === 0) {
    throw new _errors.ErrorWithStack(`No instances found with props:\n${(0, _prettyFormat.default)(props)}`, getAllByPropsFn);
  }
  return results;
};
const UNSAFE_queryByProps = instance => function queryByPropsFn(props) {
  try {
    return UNSAFE_getByProps(instance)(props);
  } catch (error) {
    return (0, _errors.createQueryByError)(error, queryByPropsFn);
  }
};
const UNSAFE_queryAllByProps = instance => props => {
  try {
    return UNSAFE_getAllByProps(instance)(props);
  } catch {
    return [];
  }
};

// Unsafe aliases

// TODO: migrate to makeQueries pattern
const bindUnsafeByPropsQueries = instance => ({
  UNSAFE_getByProps: UNSAFE_getByProps(instance),
  UNSAFE_getAllByProps: UNSAFE_getAllByProps(instance),
  UNSAFE_queryByProps: UNSAFE_queryByProps(instance),
  UNSAFE_queryAllByProps: UNSAFE_queryAllByProps(instance)
});
exports.bindUnsafeByPropsQueries = bindUnsafeByPropsQueries;
//# sourceMappingURL=unsafe-props.js.map