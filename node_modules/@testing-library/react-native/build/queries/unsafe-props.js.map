{"version": 3, "file": "unsafe-props.js", "names": ["_prettyFormat", "_interopRequireDefault", "require", "_errors", "e", "__esModule", "default", "UNSAFE_getByProps", "instance", "getByPropsFn", "props", "findByProps", "error", "ErrorWithStack", "prepareErrorMessage", "UNSAFE_getAllByProps", "getAllByPropsFn", "results", "findAllByProps", "length", "prettyFormat", "UNSAFE_queryByProps", "queryByPropsFn", "createQueryByError", "UNSAFE_queryAllByProps", "bindUnsafeByPropsQueries", "exports"], "sources": ["../../src/queries/unsafe-props.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport prettyFormat from 'pretty-format';\n\nimport { ErrorWithStack, prepareErrorMessage } from '../helpers/errors';\nimport { createQueryByError } from '../helpers/errors';\n\ntype Props = Record<string, unknown>;\n\nconst UNSAFE_getByProps = (instance: ReactTestInstance): ((props: Props) => ReactTestInstance) =>\n  function getByPropsFn(props: Props) {\n    try {\n      return instance.findByProps(props);\n    } catch (error) {\n      throw new ErrorWithStack(prepareErrorMessage(error), getByPropsFn);\n    }\n  };\n\nconst UNSAFE_getAllByProps = (\n  instance: ReactTestInstance,\n): ((props: Props) => Array<ReactTestInstance>) =>\n  function getAllByPropsFn(props: Props) {\n    const results = instance.findAllByProps(props);\n    if (results.length === 0) {\n      throw new ErrorWithStack(\n        `No instances found with props:\\n${prettyFormat(props)}`,\n        getAllByPropsFn,\n      );\n    }\n    return results;\n  };\n\nconst UNSAFE_queryByProps = (\n  instance: ReactTestInstance,\n): ((props: Props) => ReactTestInstance | null) =>\n  function queryByPropsFn(props: Props) {\n    try {\n      return UNSAFE_getByProps(instance)(props);\n    } catch (error) {\n      return createQueryByError(error, queryByPropsFn);\n    }\n  };\n\nconst UNSAFE_queryAllByProps =\n  (instance: ReactTestInstance): ((props: Props) => Array<ReactTestInstance>) =>\n  (props: Props) => {\n    try {\n      return UNSAFE_getAllByProps(instance)(props);\n    } catch {\n      return [];\n    }\n  };\n\n// Unsafe aliases\nexport type UnsafeByPropsQueries = {\n  UNSAFE_getByProps: (props: Props) => ReactTestInstance;\n  UNSAFE_getAllByProps: (props: Props) => Array<ReactTestInstance>;\n  UNSAFE_queryByProps: (props: Props) => ReactTestInstance | null;\n  UNSAFE_queryAllByProps: (props: Props) => Array<ReactTestInstance>;\n};\n\n// TODO: migrate to makeQueries pattern\nexport const bindUnsafeByPropsQueries = (instance: ReactTestInstance): UnsafeByPropsQueries => ({\n  UNSAFE_getByProps: UNSAFE_getByProps(instance),\n  UNSAFE_getAllByProps: UNSAFE_getAllByProps(instance),\n  UNSAFE_queryByProps: UNSAFE_queryByProps(instance),\n  UNSAFE_queryAllByProps: UNSAFE_queryAllByProps(instance),\n});\n"], "mappings": ";;;;;;AACA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAAwE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAKxE,MAAMG,iBAAiB,GAAIC,QAA2B,IACpD,SAASC,YAAYA,CAACC,KAAY,EAAE;EAClC,IAAI;IACF,OAAOF,QAAQ,CAACG,WAAW,CAACD,KAAK,CAAC;EACpC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAM,IAAIC,sBAAc,CAAC,IAAAC,2BAAmB,EAACF,KAAK,CAAC,EAAEH,YAAY,CAAC;EACpE;AACF,CAAC;AAEH,MAAMM,oBAAoB,GACxBP,QAA2B,IAE3B,SAASQ,eAAeA,CAACN,KAAY,EAAE;EACrC,MAAMO,OAAO,GAAGT,QAAQ,CAACU,cAAc,CAACR,KAAK,CAAC;EAC9C,IAAIO,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,MAAM,IAAIN,sBAAc,CACtB,mCAAmC,IAAAO,qBAAY,EAACV,KAAK,CAAC,EAAE,EACxDM,eACF,CAAC;EACH;EACA,OAAOC,OAAO;AAChB,CAAC;AAEH,MAAMI,mBAAmB,GACvBb,QAA2B,IAE3B,SAASc,cAAcA,CAACZ,KAAY,EAAE;EACpC,IAAI;IACF,OAAOH,iBAAiB,CAACC,QAAQ,CAAC,CAACE,KAAK,CAAC;EAC3C,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,OAAO,IAAAW,0BAAkB,EAACX,KAAK,EAAEU,cAAc,CAAC;EAClD;AACF,CAAC;AAEH,MAAME,sBAAsB,GACzBhB,QAA2B,IAC3BE,KAAY,IAAK;EAChB,IAAI;IACF,OAAOK,oBAAoB,CAACP,QAAQ,CAAC,CAACE,KAAK,CAAC;EAC9C,CAAC,CAAC,MAAM;IACN,OAAO,EAAE;EACX;AACF,CAAC;;AAEH;;AAQA;AACO,MAAMe,wBAAwB,GAAIjB,QAA2B,KAA4B;EAC9FD,iBAAiB,EAAEA,iBAAiB,CAACC,QAAQ,CAAC;EAC9CO,oBAAoB,EAAEA,oBAAoB,CAACP,QAAQ,CAAC;EACpDa,mBAAmB,EAAEA,mBAAmB,CAACb,QAAQ,CAAC;EAClDgB,sBAAsB,EAAEA,sBAAsB,CAAChB,QAAQ;AACzD,CAAC,CAAC;AAACkB,OAAA,CAAAD,wBAAA,GAAAA,wBAAA", "ignoreList": []}