"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.bindUnsafeByTypeQueries = void 0;
var _errors = require("../helpers/errors");
// eslint-disable-next-line @typescript-eslint/no-explicit-any

const UNSAFE_getByType = instance => function getByTypeFn(type) {
  try {
    return instance.findByType(type);
  } catch (error) {
    throw new _errors.ErrorWithStack((0, _errors.prepareErrorMessage)(error), getByTypeFn);
  }
};
const UNSAFE_getAllByType = instance => function getAllByTypeFn(type) {
  const results = instance.findAllByType(type);
  if (results.length === 0) {
    throw new _errors.ErrorWithStack('No instances found', getAllByTypeFn);
  }
  return results;
};
const UNSAFE_queryByType = instance => function queryByTypeFn(type) {
  try {
    return UNSAFE_getByType(instance)(type);
  } catch (error) {
    return (0, _errors.createQueryByError)(error, queryByTypeFn);
  }
};
const UNSAFE_queryAllByType = instance => type => {
  try {
    return UNSAFE_getAllByType(instance)(type);
  } catch {
    return [];
  }
};

// Unsafe aliases

// TODO: migrate to makeQueries pattern
const bindUnsafeByTypeQueries = instance => ({
  UNSAFE_getByType: UNSAFE_getByType(instance),
  UNSAFE_getAllByType: UNSAFE_getAllByType(instance),
  UNSAFE_queryByType: UNSAFE_queryByType(instance),
  UNSAFE_queryAllByType: UNSAFE_queryAllByType(instance)
});
exports.bindUnsafeByTypeQueries = bindUnsafeByTypeQueries;
//# sourceMappingURL=unsafe-type.js.map