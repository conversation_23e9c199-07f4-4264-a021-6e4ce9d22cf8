{"version": 3, "file": "options.js", "names": [], "sources": ["../../src/queries/options.ts"], "sourcesContent": ["import type { NormalizerFn } from '../matches';\n\nexport type CommonQueryOptions = {\n  /** Should query include elements hidden from accessibility. */\n  includeHiddenElements?: boolean;\n\n  /** RTL-compatibile alias to `includeHiddenElements`. */\n  hidden?: boolean;\n};\n\nexport type TextMatchOptions = {\n  exact?: boolean;\n  normalizer?: NormalizerFn;\n};\n"], "mappings": "", "ignoreList": []}