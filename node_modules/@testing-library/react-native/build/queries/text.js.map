{"version": 3, "file": "text.js", "names": ["_findAll", "require", "_hostComponentNames", "_matchTextContent", "_makeQueries", "queryAllByText", "instance", "queryAllByTextFn", "text", "options", "findAll", "node", "isHostText", "matchTextContent", "matchDeepestOnly", "getMultipleError", "String", "getMissingError", "get<PERSON>y", "getAllBy", "queryBy", "queryAllBy", "find<PERSON><PERSON>", "findAllBy", "makeQueries", "bindByTextQueries", "getByText", "getAllByText", "queryByText", "findByText", "findAllByText", "exports"], "sources": ["../../src/queries/text.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { findAll } from '../helpers/find-all';\nimport { isHostText } from '../helpers/host-component-names';\nimport { matchTextContent } from '../helpers/matchers/match-text-content';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport type {\n  FindAllByQuery,\n  FindByQuery,\n  GetAllByQuery,\n  GetByQuery,\n  QueryAllByQuery,\n  QueryByQuery,\n} from './make-queries';\nimport { makeQueries } from './make-queries';\nimport type { CommonQueryOptions } from './options';\n\ntype ByTextOptions = CommonQueryOptions & TextMatchOptions;\n\nconst queryAllByText = (instance: ReactTestInstance): QueryAllByQuery<TextMatch, ByTextOptions> =>\n  function queryAllByTextFn(text, options = {}) {\n    return findAll(instance, (node) => isHostText(node) && matchTextContent(node, text, options), {\n      ...options,\n      matchDeepestOnly: true,\n    });\n  };\n\nconst getMultipleError = (text: TextMatch) => `Found multiple elements with text: ${String(text)}`;\n\nconst getMissingError = (text: TextMatch) => `Unable to find an element with text: ${String(text)}`;\n\nconst { getBy, getAllBy, queryBy, queryAllBy, findBy, findAllBy } = makeQueries(\n  queryAllByText,\n  getMissingError,\n  getMultipleError,\n);\n\nexport type ByTextQueries = {\n  getByText: GetByQuery<TextMatch, ByTextOptions>;\n  getAllByText: GetAllByQuery<TextMatch, ByTextOptions>;\n  queryByText: QueryByQuery<TextMatch, ByTextOptions>;\n  queryAllByText: QueryAllByQuery<TextMatch, ByTextOptions>;\n  findByText: FindByQuery<TextMatch, ByTextOptions>;\n  findAllByText: FindAllByQuery<TextMatch, ByTextOptions>;\n};\n\nexport const bindByTextQueries = (instance: ReactTestInstance): ByTextQueries => ({\n  getByText: getBy(instance),\n  getAllByText: getAllBy(instance),\n  queryByText: queryBy(instance),\n  queryAllByText: queryAllBy(instance),\n  findByText: findBy(instance),\n  findAllByText: findAllBy(instance),\n});\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AAUA,IAAAG,YAAA,GAAAH,OAAA;AAKA,MAAMI,cAAc,GAAIC,QAA2B,IACjD,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAACJ,QAAQ,EAAGK,IAAI,IAAK,IAAAC,8BAAU,EAACD,IAAI,CAAC,IAAI,IAAAE,kCAAgB,EAACF,IAAI,EAAEH,IAAI,EAAEC,OAAO,CAAC,EAAE;IAC5F,GAAGA,OAAO;IACVK,gBAAgB,EAAE;EACpB,CAAC,CAAC;AACJ,CAAC;AAEH,MAAMC,gBAAgB,GAAIP,IAAe,IAAK,sCAAsCQ,MAAM,CAACR,IAAI,CAAC,EAAE;AAElG,MAAMS,eAAe,GAAIT,IAAe,IAAK,wCAAwCQ,MAAM,CAACR,IAAI,CAAC,EAAE;AAEnG,MAAM;EAAEU,KAAK;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAU,CAAC,GAAG,IAAAC,wBAAW,EAC7EnB,cAAc,EACdY,eAAe,EACfF,gBACF,CAAC;AAWM,MAAMU,iBAAiB,GAAInB,QAA2B,KAAqB;EAChFoB,SAAS,EAAER,KAAK,CAACZ,QAAQ,CAAC;EAC1BqB,YAAY,EAAER,QAAQ,CAACb,QAAQ,CAAC;EAChCsB,WAAW,EAAER,OAAO,CAACd,QAAQ,CAAC;EAC9BD,cAAc,EAAEgB,UAAU,CAACf,QAAQ,CAAC;EACpCuB,UAAU,EAAEP,MAAM,CAAChB,QAAQ,CAAC;EAC5BwB,aAAa,EAAEP,SAAS,CAACjB,QAAQ;AACnC,CAAC,CAAC;AAACyB,OAAA,CAAAN,iBAAA,GAAAA,iBAAA", "ignoreList": []}