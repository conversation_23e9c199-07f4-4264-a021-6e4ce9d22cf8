{"version": 3, "file": "role.js", "names": ["_accessibility", "require", "_findAll", "_matchAccessibilityState", "_matchAccessibilityValue", "_matchStringProp", "_within", "_makeQueries", "matchAccessibleNameIfNeeded", "node", "name", "queryAllByText", "queryAllByLabelText", "getQueriesForElement", "length", "matchAccessibleStateIfNeeded", "options", "matchAccessibilityState", "matchAccessibilityValueIfNeeded", "value", "matchAccessibilityValue", "queryAllByRole", "instance", "queryAllByRoleFn", "role", "normalizedRole", "normalizeRole", "findAll", "isAccessibilityElement", "matchStringProp", "getRole", "formatQueryParams", "params", "String", "push", "accessibilityStateKeys", "for<PERSON>ach", "stateKey", "undefined", "accessibilityValueKeys", "valueKey", "join", "getMultipleError", "getMissingError", "get<PERSON>y", "getAllBy", "queryBy", "queryAllBy", "find<PERSON><PERSON>", "findAllBy", "makeQueries", "bindByRoleQueries", "getByRole", "getAllByRole", "queryByRole", "findByRole", "findAllByRole", "exports"], "sources": ["../../src/queries/role.ts"], "sourcesContent": ["import type { AccessibilityRole, Role } from 'react-native';\nimport type { ReactTestInstance } from 'react-test-renderer';\n\nimport {\n  accessibilityStateKeys,\n  accessibilityValueKeys,\n  getRole,\n  isAccessibilityElement,\n  normalizeRole,\n} from '../helpers/accessibility';\nimport { findAll } from '../helpers/find-all';\nimport type { AccessibilityStateMatcher } from '../helpers/matchers/match-accessibility-state';\nimport { matchAccessibilityState } from '../helpers/matchers/match-accessibility-state';\nimport type { AccessibilityValueMatcher } from '../helpers/matchers/match-accessibility-value';\nimport { matchAccessibilityValue } from '../helpers/matchers/match-accessibility-value';\nimport { matchStringProp } from '../helpers/matchers/match-string-prop';\nimport type { TextMatch } from '../matches';\nimport type { StringWithAutocomplete } from '../types';\nimport { getQueriesForElement } from '../within';\nimport type {\n  FindAllByQ<PERSON><PERSON>,\n  <PERSON><PERSON>yQuery,\n  <PERSON>All<PERSON>y<PERSON><PERSON>y,\n  GetByQuery,\n  Query<PERSON>llByQuery,\n  QueryByQuery,\n} from './make-queries';\nimport { makeQueries } from './make-queries';\nimport type { CommonQueryOptions } from './options';\n\nexport type ByRoleMatcher = StringWithAutocomplete<AccessibilityRole | Role> | RegExp;\n\nexport type ByRoleOptions = CommonQueryOptions &\n  AccessibilityStateMatcher & {\n    name?: TextMatch;\n    value?: AccessibilityValueMatcher;\n  };\n\nconst matchAccessibleNameIfNeeded = (node: ReactTestInstance, name?: TextMatch) => {\n  if (name == null) return true;\n\n  const { queryAllByText, queryAllByLabelText } = getQueriesForElement(node);\n  return queryAllByText(name).length > 0 || queryAllByLabelText(name).length > 0;\n};\n\nconst matchAccessibleStateIfNeeded = (node: ReactTestInstance, options?: ByRoleOptions) => {\n  return options != null ? matchAccessibilityState(node, options) : true;\n};\n\nconst matchAccessibilityValueIfNeeded = (\n  node: ReactTestInstance,\n  value?: AccessibilityValueMatcher,\n) => {\n  return value != null ? matchAccessibilityValue(node, value) : true;\n};\n\nconst queryAllByRole = (\n  instance: ReactTestInstance,\n): QueryAllByQuery<ByRoleMatcher, ByRoleOptions> =>\n  function queryAllByRoleFn(role, options) {\n    const normalizedRole = typeof role === 'string' ? normalizeRole(role) : role;\n    return findAll(\n      instance,\n      (node) =>\n        // run the cheapest checks first, and early exit to avoid unneeded computations\n        isAccessibilityElement(node) &&\n        matchStringProp(getRole(node), normalizedRole) &&\n        matchAccessibleStateIfNeeded(node, options) &&\n        matchAccessibilityValueIfNeeded(node, options?.value) &&\n        matchAccessibleNameIfNeeded(node, options?.name),\n      options,\n    );\n  };\n\nconst formatQueryParams = (role: TextMatch, options: ByRoleOptions = {}) => {\n  const params = [`role: ${String(role)}`];\n\n  if (options.name) {\n    params.push(`name: ${String(options.name)}`);\n  }\n\n  accessibilityStateKeys.forEach((stateKey) => {\n    if (options[stateKey] !== undefined) {\n      params.push(`${stateKey} state: ${options[stateKey]}`);\n    }\n  });\n\n  accessibilityValueKeys.forEach((valueKey) => {\n    if (options?.value?.[valueKey] !== undefined) {\n      params.push(`${valueKey} value: ${options?.value?.[valueKey]}`);\n    }\n  });\n\n  return params.join(', ');\n};\n\nconst getMultipleError = (role: TextMatch, options?: ByRoleOptions) =>\n  `Found multiple elements with ${formatQueryParams(role, options)}`;\n\nconst getMissingError = (role: TextMatch, options?: ByRoleOptions) =>\n  `Unable to find an element with ${formatQueryParams(role, options)}`;\n\nconst { getBy, getAllBy, queryBy, queryAllBy, findBy, findAllBy } = makeQueries(\n  queryAllByRole,\n  getMissingError,\n  getMultipleError,\n);\n\nexport type ByRoleQueries = {\n  getByRole: GetByQuery<ByRoleMatcher, ByRoleOptions>;\n  getAllByRole: GetAllByQuery<ByRoleMatcher, ByRoleOptions>;\n  queryByRole: QueryByQuery<ByRoleMatcher, ByRoleOptions>;\n  queryAllByRole: QueryAllByQuery<ByRoleMatcher, ByRoleOptions>;\n  findByRole: FindByQuery<ByRoleMatcher, ByRoleOptions>;\n  findAllByRole: FindAllByQuery<ByRoleMatcher, ByRoleOptions>;\n};\n\nexport const bindByRoleQueries = (instance: ReactTestInstance): ByRoleQueries => ({\n  getByRole: getBy(instance),\n  getAllByRole: getAllBy(instance),\n  queryByRole: queryBy(instance),\n  queryAllByRole: queryAllBy(instance),\n  findByRole: findBy(instance),\n  findAllByRole: findAllBy(instance),\n});\n"], "mappings": ";;;;;;AAGA,IAAAA,cAAA,GAAAC,OAAA;AAOA,IAAAC,QAAA,GAAAD,OAAA;AAEA,IAAAE,wBAAA,GAAAF,OAAA;AAEA,IAAAG,wBAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AAGA,IAAAK,OAAA,GAAAL,OAAA;AASA,IAAAM,YAAA,GAAAN,OAAA;AAWA,MAAMO,2BAA2B,GAAGA,CAACC,IAAuB,EAAEC,IAAgB,KAAK;EACjF,IAAIA,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;EAE7B,MAAM;IAAEC,cAAc;IAAEC;EAAoB,CAAC,GAAG,IAAAC,4BAAoB,EAACJ,IAAI,CAAC;EAC1E,OAAOE,cAAc,CAACD,IAAI,CAAC,CAACI,MAAM,GAAG,CAAC,IAAIF,mBAAmB,CAACF,IAAI,CAAC,CAACI,MAAM,GAAG,CAAC;AAChF,CAAC;AAED,MAAMC,4BAA4B,GAAGA,CAACN,IAAuB,EAAEO,OAAuB,KAAK;EACzF,OAAOA,OAAO,IAAI,IAAI,GAAG,IAAAC,gDAAuB,EAACR,IAAI,EAAEO,OAAO,CAAC,GAAG,IAAI;AACxE,CAAC;AAED,MAAME,+BAA+B,GAAGA,CACtCT,IAAuB,EACvBU,KAAiC,KAC9B;EACH,OAAOA,KAAK,IAAI,IAAI,GAAG,IAAAC,gDAAuB,EAACX,IAAI,EAAEU,KAAK,CAAC,GAAG,IAAI;AACpE,CAAC;AAED,MAAME,cAAc,GAClBC,QAA2B,IAE3B,SAASC,gBAAgBA,CAACC,IAAI,EAAER,OAAO,EAAE;EACvC,MAAMS,cAAc,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG,IAAAE,4BAAa,EAACF,IAAI,CAAC,GAAGA,IAAI;EAC5E,OAAO,IAAAG,gBAAO,EACZL,QAAQ,EACPb,IAAI;EACH;EACA,IAAAmB,qCAAsB,EAACnB,IAAI,CAAC,IAC5B,IAAAoB,gCAAe,EAAC,IAAAC,sBAAO,EAACrB,IAAI,CAAC,EAAEgB,cAAc,CAAC,IAC9CV,4BAA4B,CAACN,IAAI,EAAEO,OAAO,CAAC,IAC3CE,+BAA+B,CAACT,IAAI,EAAEO,OAAO,EAAEG,KAAK,CAAC,IACrDX,2BAA2B,CAACC,IAAI,EAAEO,OAAO,EAAEN,IAAI,CAAC,EAClDM,OACF,CAAC;AACH,CAAC;AAEH,MAAMe,iBAAiB,GAAGA,CAACP,IAAe,EAAER,OAAsB,GAAG,CAAC,CAAC,KAAK;EAC1E,MAAMgB,MAAM,GAAG,CAAC,SAASC,MAAM,CAACT,IAAI,CAAC,EAAE,CAAC;EAExC,IAAIR,OAAO,CAACN,IAAI,EAAE;IAChBsB,MAAM,CAACE,IAAI,CAAC,SAASD,MAAM,CAACjB,OAAO,CAACN,IAAI,CAAC,EAAE,CAAC;EAC9C;EAEAyB,qCAAsB,CAACC,OAAO,CAAEC,QAAQ,IAAK;IAC3C,IAAIrB,OAAO,CAACqB,QAAQ,CAAC,KAAKC,SAAS,EAAE;MACnCN,MAAM,CAACE,IAAI,CAAC,GAAGG,QAAQ,WAAWrB,OAAO,CAACqB,QAAQ,CAAC,EAAE,CAAC;IACxD;EACF,CAAC,CAAC;EAEFE,qCAAsB,CAACH,OAAO,CAAEI,QAAQ,IAAK;IAC3C,IAAIxB,OAAO,EAAEG,KAAK,GAAGqB,QAAQ,CAAC,KAAKF,SAAS,EAAE;MAC5CN,MAAM,CAACE,IAAI,CAAC,GAAGM,QAAQ,WAAWxB,OAAO,EAAEG,KAAK,GAAGqB,QAAQ,CAAC,EAAE,CAAC;IACjE;EACF,CAAC,CAAC;EAEF,OAAOR,MAAM,CAACS,IAAI,CAAC,IAAI,CAAC;AAC1B,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAAClB,IAAe,EAAER,OAAuB,KAChE,gCAAgCe,iBAAiB,CAACP,IAAI,EAAER,OAAO,CAAC,EAAE;AAEpE,MAAM2B,eAAe,GAAGA,CAACnB,IAAe,EAAER,OAAuB,KAC/D,kCAAkCe,iBAAiB,CAACP,IAAI,EAAER,OAAO,CAAC,EAAE;AAEtE,MAAM;EAAE4B,KAAK;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAU,CAAC,GAAG,IAAAC,wBAAW,EAC7E7B,cAAc,EACdsB,eAAe,EACfD,gBACF,CAAC;AAWM,MAAMS,iBAAiB,GAAI7B,QAA2B,KAAqB;EAChF8B,SAAS,EAAER,KAAK,CAACtB,QAAQ,CAAC;EAC1B+B,YAAY,EAAER,QAAQ,CAACvB,QAAQ,CAAC;EAChCgC,WAAW,EAAER,OAAO,CAACxB,QAAQ,CAAC;EAC9BD,cAAc,EAAE0B,UAAU,CAACzB,QAAQ,CAAC;EACpCiC,UAAU,EAAEP,MAAM,CAAC1B,QAAQ,CAAC;EAC5BkC,aAAa,EAAEP,SAAS,CAAC3B,QAAQ;AACnC,CAAC,CAAC;AAACmC,OAAA,CAAAN,iBAAA,GAAAA,iBAAA", "ignoreList": []}