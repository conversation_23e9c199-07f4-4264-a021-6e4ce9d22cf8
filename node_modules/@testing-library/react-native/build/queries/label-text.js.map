{"version": 3, "file": "label-text.js", "names": ["_findAll", "require", "_matchLabelText", "_makeQueries", "queryAllByLabelText", "instance", "text", "queryOptions", "findAll", "node", "matchAccessibilityLabel", "getMultipleError", "labelText", "String", "getMissingError", "get<PERSON>y", "getAllBy", "queryBy", "queryAllBy", "find<PERSON><PERSON>", "findAllBy", "makeQueries", "bindByLabelTextQueries", "getByLabelText", "getAllByLabelText", "queryByLabelText", "findByLabelText", "findAllByLabelText", "exports"], "sources": ["../../src/queries/label-text.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { findAll } from '../helpers/find-all';\nimport { matchAccessibilityLabel } from '../helpers/matchers/match-label-text';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport type {\n  FindAllByQuery,\n  FindByQuery,\n  GetAllByQuery,\n  GetByQuery,\n  QueryAllByQuery,\n  QueryByQuery,\n} from './make-queries';\nimport { makeQueries } from './make-queries';\nimport type { CommonQueryOptions } from './options';\n\ntype ByLabelTextOptions = CommonQueryOptions & TextMatchOptions;\n\nfunction queryAllByLabelText(instance: ReactTestInstance) {\n  return (text: TextMatch, queryOptions?: ByLabelTextOptions) => {\n    return findAll(\n      instance,\n      (node) => matchAccessibilityLabel(node, text, queryOptions),\n      queryOptions,\n    );\n  };\n}\n\nconst getMultipleError = (labelText: TextMatch) =>\n  `Found multiple elements with accessibility label: ${String(labelText)} `;\nconst getMissingError = (labelText: TextMatch) =>\n  `Unable to find an element with accessibility label: ${String(labelText)}`;\n\nconst { getBy, getAllBy, queryBy, queryAllBy, findBy, findAllBy } = makeQueries(\n  queryAllByLabelText,\n  getMissingError,\n  getMultipleError,\n);\n\nexport type ByLabelTextQueries = {\n  getByLabelText: GetByQuery<TextMatch, ByLabelTextOptions>;\n  getAllByLabelText: GetAllByQuery<TextMatch, ByLabelTextOptions>;\n  queryByLabelText: QueryByQuery<TextMatch, ByLabelTextOptions>;\n  queryAllByLabelText: QueryAllByQuery<TextMatch, ByLabelTextOptions>;\n  findByLabelText: FindByQuery<TextMatch, ByLabelTextOptions>;\n  findAllByLabelText: FindAllByQuery<TextMatch, ByLabelTextOptions>;\n};\n\nexport const bindByLabelTextQueries = (instance: ReactTestInstance): ByLabelTextQueries => ({\n  getByLabelText: getBy(instance),\n  getAllByLabelText: getAllBy(instance),\n  queryByLabelText: queryBy(instance),\n  queryAllByLabelText: queryAllBy(instance),\n  findByLabelText: findBy(instance),\n  findAllByLabelText: findAllBy(instance),\n});\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAUA,IAAAE,YAAA,GAAAF,OAAA;AAKA,SAASG,mBAAmBA,CAACC,QAA2B,EAAE;EACxD,OAAO,CAACC,IAAe,EAAEC,YAAiC,KAAK;IAC7D,OAAO,IAAAC,gBAAO,EACZH,QAAQ,EACPI,IAAI,IAAK,IAAAC,uCAAuB,EAACD,IAAI,EAAEH,IAAI,EAAEC,YAAY,CAAC,EAC3DA,YACF,CAAC;EACH,CAAC;AACH;AAEA,MAAMI,gBAAgB,GAAIC,SAAoB,IAC5C,qDAAqDC,MAAM,CAACD,SAAS,CAAC,GAAG;AAC3E,MAAME,eAAe,GAAIF,SAAoB,IAC3C,uDAAuDC,MAAM,CAACD,SAAS,CAAC,EAAE;AAE5E,MAAM;EAAEG,KAAK;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAU,CAAC,GAAG,IAAAC,wBAAW,EAC7EjB,mBAAmB,EACnBU,eAAe,EACfH,gBACF,CAAC;AAWM,MAAMW,sBAAsB,GAAIjB,QAA2B,KAA0B;EAC1FkB,cAAc,EAAER,KAAK,CAACV,QAAQ,CAAC;EAC/BmB,iBAAiB,EAAER,QAAQ,CAACX,QAAQ,CAAC;EACrCoB,gBAAgB,EAAER,OAAO,CAACZ,QAAQ,CAAC;EACnCD,mBAAmB,EAAEc,UAAU,CAACb,QAAQ,CAAC;EACzCqB,eAAe,EAAEP,MAAM,CAACd,QAAQ,CAAC;EACjCsB,kBAAkB,EAAEP,SAAS,CAACf,QAAQ;AACxC,CAAC,CAAC;AAACuB,OAAA,CAAAN,sBAAA,GAAAA,sBAAA", "ignoreList": []}