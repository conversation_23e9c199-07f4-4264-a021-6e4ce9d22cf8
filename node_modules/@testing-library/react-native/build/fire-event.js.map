{"version": 3, "file": "fire-event.js", "names": ["_act", "_interopRequireDefault", "require", "_event<PERSON><PERSON><PERSON>", "_componentTree", "_hostComponentNames", "_pointerEvents", "_textInput", "_nativeState", "e", "__esModule", "default", "isTouchResponder", "element", "isHostElement", "Boolean", "props", "onStartShouldSetResponder", "isHostTextInput", "eventsAffectedByPointerEventsProp", "Set", "textInputEventsIgnoringEditableProp", "isEventEnabled", "eventName", "nearestTouchResponder", "isEditableTextInput", "has", "isPointerEventEnabled", "touchStart", "touchMove", "onMoveShouldSetResponder", "undefined", "findEventHandler", "touchResponder", "handler", "getEventHandler", "loose", "parent", "fireEvent", "data", "isElementMounted", "setNativeStateIfNeeded", "returnValue", "act", "press", "changeText", "scroll", "_default", "exports", "scrollEventNames", "value", "nativeState", "valueForElement", "set", "isHostScrollView", "contentOffset", "tryGetContentOffset", "contentOffsetForElement", "event", "nativeEvent", "x", "y", "Number", "isFinite"], "sources": ["../src/fire-event.ts"], "sourcesContent": ["import type {\n  PressableProps,\n  ScrollViewProps,\n  TextInputProps,\n  TextProps,\n  ViewProps,\n} from 'react-native';\nimport type { ReactTestInstance } from 'react-test-renderer';\n\nimport act from './act';\nimport { getEventHandler } from './event-handler';\nimport { isElementMounted, isHostElement } from './helpers/component-tree';\nimport { isHostScrollView, isHostTextInput } from './helpers/host-component-names';\nimport { isPointerEventEnabled } from './helpers/pointer-events';\nimport { isEditableTextInput } from './helpers/text-input';\nimport { nativeState } from './native-state';\nimport type { Point, StringWithAutocomplete } from './types';\n\ntype EventHandler = (...args: unknown[]) => unknown;\n\nexport function isTouchResponder(element: ReactTestInstance) {\n  if (!isHostElement(element)) {\n    return false;\n  }\n\n  return Boolean(element.props.onStartShouldSetResponder) || isHostTextInput(element);\n}\n\n/**\n * List of events affected by `pointerEvents` prop.\n *\n * Note: `fireEvent` is accepting both `press` and `onPress` for event names,\n * so we need cover both forms.\n */\nconst eventsAffectedByPointerEventsProp = new Set(['press', 'onPress']);\n\n/**\n * List of `TextInput` events not affected by `editable` prop.\n *\n * Note: `fireEvent` is accepting both `press` and `onPress` for event names,\n * so we need cover both forms.\n */\nconst textInputEventsIgnoringEditableProp = new Set([\n  'contentSizeChange',\n  'onContentSizeChange',\n  'layout',\n  'onLayout',\n  'scroll',\n  'onScroll',\n]);\n\nexport function isEventEnabled(\n  element: ReactTestInstance,\n  eventName: string,\n  nearestTouchResponder?: ReactTestInstance,\n) {\n  if (nearestTouchResponder != null && isHostTextInput(nearestTouchResponder)) {\n    return (\n      isEditableTextInput(nearestTouchResponder) ||\n      textInputEventsIgnoringEditableProp.has(eventName)\n    );\n  }\n\n  if (eventsAffectedByPointerEventsProp.has(eventName) && !isPointerEventEnabled(element)) {\n    return false;\n  }\n\n  const touchStart = nearestTouchResponder?.props.onStartShouldSetResponder?.();\n  const touchMove = nearestTouchResponder?.props.onMoveShouldSetResponder?.();\n  if (touchStart || touchMove) {\n    return true;\n  }\n\n  return touchStart === undefined && touchMove === undefined;\n}\n\nfunction findEventHandler(\n  element: ReactTestInstance,\n  eventName: string,\n  nearestTouchResponder?: ReactTestInstance,\n): EventHandler | null {\n  const touchResponder = isTouchResponder(element) ? element : nearestTouchResponder;\n\n  const handler = getEventHandler(element, eventName, { loose: true });\n  if (handler && isEventEnabled(element, eventName, touchResponder)) return handler;\n\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (element.parent === null || element.parent.parent === null) {\n    return null;\n  }\n\n  return findEventHandler(element.parent, eventName, touchResponder);\n}\n\n// String union type of keys of T that start with on, stripped of 'on'\ntype EventNameExtractor<T> = keyof {\n  [K in keyof T as K extends `on${infer Rest}` ? Uncapitalize<Rest> : never]: T[K];\n};\n\ntype EventName = StringWithAutocomplete<\n  | EventNameExtractor<ViewProps>\n  | EventNameExtractor<TextProps>\n  | EventNameExtractor<TextInputProps>\n  | EventNameExtractor<PressableProps>\n  | EventNameExtractor<ScrollViewProps>\n>;\n\nfunction fireEvent(element: ReactTestInstance, eventName: EventName, ...data: unknown[]) {\n  if (!isElementMounted(element)) {\n    return;\n  }\n\n  setNativeStateIfNeeded(element, eventName, data[0]);\n\n  const handler = findEventHandler(element, eventName);\n  if (!handler) {\n    return;\n  }\n\n  let returnValue;\n  void act(() => {\n    returnValue = handler(...data);\n  });\n\n  return returnValue;\n}\n\nfireEvent.press = (element: ReactTestInstance, ...data: unknown[]) =>\n  fireEvent(element, 'press', ...data);\n\nfireEvent.changeText = (element: ReactTestInstance, ...data: unknown[]) =>\n  fireEvent(element, 'changeText', ...data);\n\nfireEvent.scroll = (element: ReactTestInstance, ...data: unknown[]) =>\n  fireEvent(element, 'scroll', ...data);\n\nexport default fireEvent;\n\nconst scrollEventNames = new Set([\n  'scroll',\n  'scrollBeginDrag',\n  'scrollEndDrag',\n  'momentumScrollBegin',\n  'momentumScrollEnd',\n]);\n\nfunction setNativeStateIfNeeded(element: ReactTestInstance, eventName: string, value: unknown) {\n  if (eventName === 'changeText' && typeof value === 'string' && isEditableTextInput(element)) {\n    nativeState.valueForElement.set(element, value);\n  }\n\n  if (scrollEventNames.has(eventName) && isHostScrollView(element)) {\n    const contentOffset = tryGetContentOffset(value);\n    if (contentOffset) {\n      nativeState.contentOffsetForElement.set(element, contentOffset);\n    }\n  }\n}\n\nfunction tryGetContentOffset(event: unknown): Point | null {\n  try {\n    // @ts-expect-error: try to extract contentOffset from the event value\n    const contentOffset = event?.nativeEvent?.contentOffset;\n    const x = contentOffset?.x;\n    const y = contentOffset?.y;\n    if (typeof x === 'number' || typeof y === 'number') {\n      return {\n        x: Number.isFinite(x) ? x : 0,\n        y: Number.isFinite(y) ? y : 0,\n      };\n    }\n  } catch {\n    // Do nothing\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;;AASA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AACA,IAAAM,YAAA,GAAAN,OAAA;AAA6C,SAAAD,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAKtC,SAASG,gBAAgBA,CAACC,OAA0B,EAAE;EAC3D,IAAI,CAAC,IAAAC,4BAAa,EAACD,OAAO,CAAC,EAAE;IAC3B,OAAO,KAAK;EACd;EAEA,OAAOE,OAAO,CAACF,OAAO,CAACG,KAAK,CAACC,yBAAyB,CAAC,IAAI,IAAAC,mCAAe,EAACL,OAAO,CAAC;AACrF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,iCAAiC,GAAG,IAAIC,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG,IAAID,GAAG,CAAC,CAClD,mBAAmB,EACnB,qBAAqB,EACrB,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,UAAU,CACX,CAAC;AAEK,SAASE,cAAcA,CAC5BT,OAA0B,EAC1BU,SAAiB,EACjBC,qBAAyC,EACzC;EACA,IAAIA,qBAAqB,IAAI,IAAI,IAAI,IAAAN,mCAAe,EAACM,qBAAqB,CAAC,EAAE;IAC3E,OACE,IAAAC,8BAAmB,EAACD,qBAAqB,CAAC,IAC1CH,mCAAmC,CAACK,GAAG,CAACH,SAAS,CAAC;EAEtD;EAEA,IAAIJ,iCAAiC,CAACO,GAAG,CAACH,SAAS,CAAC,IAAI,CAAC,IAAAI,oCAAqB,EAACd,OAAO,CAAC,EAAE;IACvF,OAAO,KAAK;EACd;EAEA,MAAMe,UAAU,GAAGJ,qBAAqB,EAAER,KAAK,CAACC,yBAAyB,GAAG,CAAC;EAC7E,MAAMY,SAAS,GAAGL,qBAAqB,EAAER,KAAK,CAACc,wBAAwB,GAAG,CAAC;EAC3E,IAAIF,UAAU,IAAIC,SAAS,EAAE;IAC3B,OAAO,IAAI;EACb;EAEA,OAAOD,UAAU,KAAKG,SAAS,IAAIF,SAAS,KAAKE,SAAS;AAC5D;AAEA,SAASC,gBAAgBA,CACvBnB,OAA0B,EAC1BU,SAAiB,EACjBC,qBAAyC,EACpB;EACrB,MAAMS,cAAc,GAAGrB,gBAAgB,CAACC,OAAO,CAAC,GAAGA,OAAO,GAAGW,qBAAqB;EAElF,MAAMU,OAAO,GAAG,IAAAC,6BAAe,EAACtB,OAAO,EAAEU,SAAS,EAAE;IAAEa,KAAK,EAAE;EAAK,CAAC,CAAC;EACpE,IAAIF,OAAO,IAAIZ,cAAc,CAACT,OAAO,EAAEU,SAAS,EAAEU,cAAc,CAAC,EAAE,OAAOC,OAAO;;EAEjF;EACA,IAAIrB,OAAO,CAACwB,MAAM,KAAK,IAAI,IAAIxB,OAAO,CAACwB,MAAM,CAACA,MAAM,KAAK,IAAI,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,OAAOL,gBAAgB,CAACnB,OAAO,CAACwB,MAAM,EAAEd,SAAS,EAAEU,cAAc,CAAC;AACpE;;AAEA;;AAaA,SAASK,SAASA,CAACzB,OAA0B,EAAEU,SAAoB,EAAE,GAAGgB,IAAe,EAAE;EACvF,IAAI,CAAC,IAAAC,+BAAgB,EAAC3B,OAAO,CAAC,EAAE;IAC9B;EACF;EAEA4B,sBAAsB,CAAC5B,OAAO,EAAEU,SAAS,EAAEgB,IAAI,CAAC,CAAC,CAAC,CAAC;EAEnD,MAAML,OAAO,GAAGF,gBAAgB,CAACnB,OAAO,EAAEU,SAAS,CAAC;EACpD,IAAI,CAACW,OAAO,EAAE;IACZ;EACF;EAEA,IAAIQ,WAAW;EACf,KAAK,IAAAC,YAAG,EAAC,MAAM;IACbD,WAAW,GAAGR,OAAO,CAAC,GAAGK,IAAI,CAAC;EAChC,CAAC,CAAC;EAEF,OAAOG,WAAW;AACpB;AAEAJ,SAAS,CAACM,KAAK,GAAG,CAAC/B,OAA0B,EAAE,GAAG0B,IAAe,KAC/DD,SAAS,CAACzB,OAAO,EAAE,OAAO,EAAE,GAAG0B,IAAI,CAAC;AAEtCD,SAAS,CAACO,UAAU,GAAG,CAAChC,OAA0B,EAAE,GAAG0B,IAAe,KACpED,SAAS,CAACzB,OAAO,EAAE,YAAY,EAAE,GAAG0B,IAAI,CAAC;AAE3CD,SAAS,CAACQ,MAAM,GAAG,CAACjC,OAA0B,EAAE,GAAG0B,IAAe,KAChED,SAAS,CAACzB,OAAO,EAAE,QAAQ,EAAE,GAAG0B,IAAI,CAAC;AAAC,IAAAQ,QAAA,GAAAC,OAAA,CAAArC,OAAA,GAEzB2B,SAAS;AAExB,MAAMW,gBAAgB,GAAG,IAAI7B,GAAG,CAAC,CAC/B,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,qBAAqB,EACrB,mBAAmB,CACpB,CAAC;AAEF,SAASqB,sBAAsBA,CAAC5B,OAA0B,EAAEU,SAAiB,EAAE2B,KAAc,EAAE;EAC7F,IAAI3B,SAAS,KAAK,YAAY,IAAI,OAAO2B,KAAK,KAAK,QAAQ,IAAI,IAAAzB,8BAAmB,EAACZ,OAAO,CAAC,EAAE;IAC3FsC,wBAAW,CAACC,eAAe,CAACC,GAAG,CAACxC,OAAO,EAAEqC,KAAK,CAAC;EACjD;EAEA,IAAID,gBAAgB,CAACvB,GAAG,CAACH,SAAS,CAAC,IAAI,IAAA+B,oCAAgB,EAACzC,OAAO,CAAC,EAAE;IAChE,MAAM0C,aAAa,GAAGC,mBAAmB,CAACN,KAAK,CAAC;IAChD,IAAIK,aAAa,EAAE;MACjBJ,wBAAW,CAACM,uBAAuB,CAACJ,GAAG,CAACxC,OAAO,EAAE0C,aAAa,CAAC;IACjE;EACF;AACF;AAEA,SAASC,mBAAmBA,CAACE,KAAc,EAAgB;EACzD,IAAI;IACF;IACA,MAAMH,aAAa,GAAGG,KAAK,EAAEC,WAAW,EAAEJ,aAAa;IACvD,MAAMK,CAAC,GAAGL,aAAa,EAAEK,CAAC;IAC1B,MAAMC,CAAC,GAAGN,aAAa,EAAEM,CAAC;IAC1B,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAO;QACLD,CAAC,EAAEE,MAAM,CAACC,QAAQ,CAACH,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;QAC7BC,CAAC,EAAEC,MAAM,CAACC,QAAQ,CAACF,CAAC,CAAC,GAAGA,CAAC,GAAG;MAC9B,CAAC;IACH;EACF,CAAC,CAAC,MAAM;IACN;EAAA;EAGF,OAAO,IAAI;AACb", "ignoreList": []}