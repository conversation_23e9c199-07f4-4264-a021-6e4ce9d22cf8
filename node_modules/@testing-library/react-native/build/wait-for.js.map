{"version": 3, "file": "wait-for.js", "names": ["_config", "require", "_flushMicroTasks", "_errors", "_timers", "_wrapAsync", "DEFAULT_INTERVAL", "waitForInternal", "expectation", "timeout", "getConfig", "asyncUtilTimeout", "interval", "stackTraceError", "onTimeout", "TypeError", "Promise", "resolve", "reject", "lastError", "intervalId", "finished", "promiseStatus", "overallTimeoutTimer", "usingFakeTimers", "jestFakeTimersAreEnabled", "checkExpectation", "fakeTimeRemaining", "error", "Error", "copyStackTrace", "handleTimeout", "jest", "advanceTimersByTime", "flushMicroTasks", "setTimeout", "setInterval", "checkRealTimersCallback", "onDone", "done", "clearTimeout", "clearInterval", "type", "result", "then", "promiseResult", "resolvedValue", "rejectedV<PERSON>ue", "String", "waitFor", "options", "ErrorWithStack", "optionsWithStackTrace", "wrapAsync"], "sources": ["../src/wait-for.ts"], "sourcesContent": ["/* globals jest */\nimport { getConfig } from './config';\nimport { flushMicroTasks } from './flush-micro-tasks';\nimport { copyStackTrace, ErrorWithStack } from './helpers/errors';\nimport { clearTimeout, jestFakeTimersAreEnabled, setTimeout } from './helpers/timers';\nimport { wrapAsync } from './helpers/wrap-async';\n\nconst DEFAULT_INTERVAL = 50;\n\nexport type WaitForOptions = {\n  timeout?: number;\n  interval?: number;\n  stackTraceError?: ErrorWithStack;\n  onTimeout?: (error: Error) => Error;\n};\n\nfunction waitForInternal<T>(\n  expectation: () => T,\n  {\n    timeout = getConfig().asyncUtilTimeout,\n    interval = DEFAULT_INTERVAL,\n    stackTraceError,\n    onTimeout,\n  }: WaitForOptions,\n): Promise<T> {\n  if (typeof expectation !== 'function') {\n    throw new TypeError('Received `expectation` arg must be a function');\n  }\n\n  // eslint-disable-next-line no-async-promise-executor\n  return new Promise(async (resolve, reject) => {\n    let lastError: unknown, intervalId: ReturnType<typeof setTimeout>;\n    let finished = false;\n    let promiseStatus = 'idle';\n\n    let overallTimeoutTimer: NodeJS.Timeout | null = null;\n\n    const usingFakeTimers = jestFakeTimersAreEnabled();\n\n    if (usingFakeTimers) {\n      checkExpectation();\n      // this is a dangerous rule to disable because it could lead to an\n      // infinite loop. However, eslint isn't smart enough to know that we're\n      // setting finished inside `onDone` which will be called when we're done\n      // waiting or when we've timed out.\n      let fakeTimeRemaining = timeout;\n      while (!finished) {\n        if (!jestFakeTimersAreEnabled()) {\n          const error = new Error(\n            `Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830`,\n          );\n          if (stackTraceError) {\n            copyStackTrace(error, stackTraceError);\n          }\n          reject(error);\n          return;\n        }\n\n        // when fake timers are used we want to simulate the interval time passing\n        if (fakeTimeRemaining <= 0) {\n          handleTimeout();\n          return;\n        } else {\n          fakeTimeRemaining -= interval;\n        }\n\n        // we *could* (maybe should?) use `advanceTimersToNextTimer` but it's\n        // possible that could make this loop go on forever if someone is using\n        // third party code that's setting up recursive timers so rapidly that\n        // the user's timer's don't get a chance to resolve. So we'll advance\n        // by an interval instead. (We have a test for this case).\n        jest.advanceTimersByTime(interval);\n\n        // It's really important that checkExpectation is run *before* we flush\n        // in-flight promises. To be honest, I'm not sure why, and I can't quite\n        // think of a way to reproduce the problem in a test, but I spent\n        // an entire day banging my head against a wall on this.\n        checkExpectation();\n\n        // In this rare case, we *need* to wait for in-flight promises\n        // to resolve before continuing. We don't need to take advantage\n        // of parallelization so we're fine.\n        // https://stackoverflow.com/a/59243586/971592\n        await flushMicroTasks();\n      }\n    } else {\n      overallTimeoutTimer = setTimeout(handleTimeout, timeout);\n      intervalId = setInterval(checkRealTimersCallback, interval);\n      checkExpectation();\n    }\n\n    function onDone(done: { type: 'result'; result: T } | { type: 'error'; error: unknown }) {\n      finished = true;\n      if (overallTimeoutTimer) {\n        clearTimeout(overallTimeoutTimer);\n      }\n\n      if (!usingFakeTimers) {\n        clearInterval(intervalId);\n      }\n\n      if (done.type === 'error') {\n        reject(done.error);\n      } else {\n        resolve(done.result);\n      }\n    }\n\n    function checkRealTimersCallback() {\n      if (jestFakeTimersAreEnabled()) {\n        const error = new Error(\n          `Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830`,\n        );\n        if (stackTraceError) {\n          copyStackTrace(error, stackTraceError);\n        }\n        return reject(error);\n      } else {\n        return checkExpectation();\n      }\n    }\n\n    function checkExpectation() {\n      if (promiseStatus === 'pending') return;\n      try {\n        const result = expectation();\n\n        // @ts-expect-error result can be a promise\n        if (typeof result?.then === 'function') {\n          const promiseResult: Promise<T> = result as unknown as Promise<T>;\n          promiseStatus = 'pending';\n          // eslint-disable-next-line promise/catch-or-return, promise/prefer-await-to-then\n          promiseResult.then(\n            (resolvedValue) => {\n              promiseStatus = 'resolved';\n              onDone({ type: 'result', result: resolvedValue });\n              return;\n            },\n            (rejectedValue) => {\n              promiseStatus = 'rejected';\n              lastError = rejectedValue;\n              return;\n            },\n          );\n        } else {\n          onDone({ type: 'result', result: result });\n        }\n        // If `callback` throws, wait for the next mutation, interval, or timeout.\n      } catch (error) {\n        // Save the most recent callback error to reject the promise with it in the event of a timeout\n        lastError = error;\n      }\n    }\n\n    function handleTimeout() {\n      let error: Error;\n      if (lastError) {\n        if (lastError instanceof Error) {\n          error = lastError;\n        } else {\n          error = new Error(String(lastError));\n        }\n\n        if (stackTraceError) {\n          copyStackTrace(error, stackTraceError);\n        }\n      } else {\n        error = new Error('Timed out in waitFor.');\n        if (stackTraceError) {\n          copyStackTrace(error, stackTraceError);\n        }\n      }\n      if (typeof onTimeout === 'function') {\n        const result = onTimeout(error);\n        if (result) {\n          error = result;\n        }\n      }\n      onDone({ type: 'error', error });\n    }\n  });\n}\n\nexport default function waitFor<T>(expectation: () => T, options?: WaitForOptions): Promise<T> {\n  // Being able to display a useful stack trace requires generating it before doing anything async\n  const stackTraceError = new ErrorWithStack('STACK_TRACE_ERROR', waitFor);\n  const optionsWithStackTrace = { stackTraceError, ...options };\n\n  return wrapAsync(() => waitForInternal(expectation, optionsWithStackTrace));\n}\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AALA;;AAOA,MAAMK,gBAAgB,GAAG,EAAE;AAS3B,SAASC,eAAeA,CACtBC,WAAoB,EACpB;EACEC,OAAO,GAAG,IAAAC,iBAAS,EAAC,CAAC,CAACC,gBAAgB;EACtCC,QAAQ,GAAGN,gBAAgB;EAC3BO,eAAe;EACfC;AACc,CAAC,EACL;EACZ,IAAI,OAAON,WAAW,KAAK,UAAU,EAAE;IACrC,MAAM,IAAIO,SAAS,CAAC,+CAA+C,CAAC;EACtE;;EAEA;EACA,OAAO,IAAIC,OAAO,CAAC,OAAOC,OAAO,EAAEC,MAAM,KAAK;IAC5C,IAAIC,SAAkB,EAAEC,UAAyC;IACjE,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,aAAa,GAAG,MAAM;IAE1B,IAAIC,mBAA0C,GAAG,IAAI;IAErD,MAAMC,eAAe,GAAG,IAAAC,gCAAwB,EAAC,CAAC;IAElD,IAAID,eAAe,EAAE;MACnBE,gBAAgB,CAAC,CAAC;MAClB;MACA;MACA;MACA;MACA,IAAIC,iBAAiB,GAAGlB,OAAO;MAC/B,OAAO,CAACY,QAAQ,EAAE;QAChB,IAAI,CAAC,IAAAI,gCAAwB,EAAC,CAAC,EAAE;UAC/B,MAAMG,KAAK,GAAG,IAAIC,KAAK,CACrB,kUACF,CAAC;UACD,IAAIhB,eAAe,EAAE;YACnB,IAAAiB,sBAAc,EAACF,KAAK,EAAEf,eAAe,CAAC;UACxC;UACAK,MAAM,CAACU,KAAK,CAAC;UACb;QACF;;QAEA;QACA,IAAID,iBAAiB,IAAI,CAAC,EAAE;UAC1BI,aAAa,CAAC,CAAC;UACf;QACF,CAAC,MAAM;UACLJ,iBAAiB,IAAIf,QAAQ;QAC/B;;QAEA;QACA;QACA;QACA;QACA;QACAoB,IAAI,CAACC,mBAAmB,CAACrB,QAAQ,CAAC;;QAElC;QACA;QACA;QACA;QACAc,gBAAgB,CAAC,CAAC;;QAElB;QACA;QACA;QACA;QACA,MAAM,IAAAQ,gCAAe,EAAC,CAAC;MACzB;IACF,CAAC,MAAM;MACLX,mBAAmB,GAAG,IAAAY,kBAAU,EAACJ,aAAa,EAAEtB,OAAO,CAAC;MACxDW,UAAU,GAAGgB,WAAW,CAACC,uBAAuB,EAAEzB,QAAQ,CAAC;MAC3Dc,gBAAgB,CAAC,CAAC;IACpB;IAEA,SAASY,MAAMA,CAACC,IAAuE,EAAE;MACvFlB,QAAQ,GAAG,IAAI;MACf,IAAIE,mBAAmB,EAAE;QACvB,IAAAiB,oBAAY,EAACjB,mBAAmB,CAAC;MACnC;MAEA,IAAI,CAACC,eAAe,EAAE;QACpBiB,aAAa,CAACrB,UAAU,CAAC;MAC3B;MAEA,IAAImB,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;QACzBxB,MAAM,CAACqB,IAAI,CAACX,KAAK,CAAC;MACpB,CAAC,MAAM;QACLX,OAAO,CAACsB,IAAI,CAACI,MAAM,CAAC;MACtB;IACF;IAEA,SAASN,uBAAuBA,CAAA,EAAG;MACjC,IAAI,IAAAZ,gCAAwB,EAAC,CAAC,EAAE;QAC9B,MAAMG,KAAK,GAAG,IAAIC,KAAK,CACrB,kUACF,CAAC;QACD,IAAIhB,eAAe,EAAE;UACnB,IAAAiB,sBAAc,EAACF,KAAK,EAAEf,eAAe,CAAC;QACxC;QACA,OAAOK,MAAM,CAACU,KAAK,CAAC;MACtB,CAAC,MAAM;QACL,OAAOF,gBAAgB,CAAC,CAAC;MAC3B;IACF;IAEA,SAASA,gBAAgBA,CAAA,EAAG;MAC1B,IAAIJ,aAAa,KAAK,SAAS,EAAE;MACjC,IAAI;QACF,MAAMqB,MAAM,GAAGnC,WAAW,CAAC,CAAC;;QAE5B;QACA,IAAI,OAAOmC,MAAM,EAAEC,IAAI,KAAK,UAAU,EAAE;UACtC,MAAMC,aAAyB,GAAGF,MAA+B;UACjErB,aAAa,GAAG,SAAS;UACzB;UACAuB,aAAa,CAACD,IAAI,CACfE,aAAa,IAAK;YACjBxB,aAAa,GAAG,UAAU;YAC1BgB,MAAM,CAAC;cAAEI,IAAI,EAAE,QAAQ;cAAEC,MAAM,EAAEG;YAAc,CAAC,CAAC;YACjD;UACF,CAAC,EACAC,aAAa,IAAK;YACjBzB,aAAa,GAAG,UAAU;YAC1BH,SAAS,GAAG4B,aAAa;YACzB;UACF,CACF,CAAC;QACH,CAAC,MAAM;UACLT,MAAM,CAAC;YAAEI,IAAI,EAAE,QAAQ;YAAEC,MAAM,EAAEA;UAAO,CAAC,CAAC;QAC5C;QACA;MACF,CAAC,CAAC,OAAOf,KAAK,EAAE;QACd;QACAT,SAAS,GAAGS,KAAK;MACnB;IACF;IAEA,SAASG,aAAaA,CAAA,EAAG;MACvB,IAAIH,KAAY;MAChB,IAAIT,SAAS,EAAE;QACb,IAAIA,SAAS,YAAYU,KAAK,EAAE;UAC9BD,KAAK,GAAGT,SAAS;QACnB,CAAC,MAAM;UACLS,KAAK,GAAG,IAAIC,KAAK,CAACmB,MAAM,CAAC7B,SAAS,CAAC,CAAC;QACtC;QAEA,IAAIN,eAAe,EAAE;UACnB,IAAAiB,sBAAc,EAACF,KAAK,EAAEf,eAAe,CAAC;QACxC;MACF,CAAC,MAAM;QACLe,KAAK,GAAG,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC1C,IAAIhB,eAAe,EAAE;UACnB,IAAAiB,sBAAc,EAACF,KAAK,EAAEf,eAAe,CAAC;QACxC;MACF;MACA,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;QACnC,MAAM6B,MAAM,GAAG7B,SAAS,CAACc,KAAK,CAAC;QAC/B,IAAIe,MAAM,EAAE;UACVf,KAAK,GAAGe,MAAM;QAChB;MACF;MACAL,MAAM,CAAC;QAAEI,IAAI,EAAE,OAAO;QAAEd;MAAM,CAAC,CAAC;IAClC;EACF,CAAC,CAAC;AACJ;AAEe,SAASqB,OAAOA,CAAIzC,WAAoB,EAAE0C,OAAwB,EAAc;EAC7F;EACA,MAAMrC,eAAe,GAAG,IAAIsC,sBAAc,CAAC,mBAAmB,EAAEF,OAAO,CAAC;EACxE,MAAMG,qBAAqB,GAAG;IAAEvC,eAAe;IAAE,GAAGqC;EAAQ,CAAC;EAE7D,OAAO,IAAAG,oBAAS,EAAC,MAAM9C,eAAe,CAACC,WAAW,EAAE4C,qBAAqB,CAAC,CAAC;AAC7E", "ignoreList": []}