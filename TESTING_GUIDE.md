# 环节设计器V2 测试指南

## 🎯 测试目标

验证环节设计器V2的新功能：
1. 步骤编辑功能
2. 自定义步骤名称
3. 步骤时长设置
4. 权限验证

## 🧪 后端API测试

### 已完成的自动化测试

后端API测试已通过，包括：

```bash
# 运行后端测试
cd /path/to/Tuanzi
source venv/bin/activate
python manage.py test events.test_api_v2 -v 2
```

**测试结果**: ✅ 所有10个测试通过

测试覆盖：
- ✅ EventStep模型的name字段
- ✅ 步骤更新API权限验证
- ✅ 步骤重新排序API
- ✅ 数据验证和错误处理

## 📱 前端功能测试

### 手动测试步骤

#### 1. 启动应用

```bash
# 启动后端
cd Tuanzi
source venv/bin/activate
python manage.py runserver

# 启动前端 (新终端)
cd TuanziApp
npm start
npm run android
```

#### 2. 测试步骤编辑功能

**前置条件**: 已登录用户，已创建至少一个环节模板

**测试步骤**:
1. 进入"环节设计器"
2. 选择一个现有模板
3. 点击任意步骤卡片
4. 验证进入"编辑步骤"页面
5. 修改步骤名称
6. 修改步骤类型
7. 修改时长
8. 点击"保存更改"
9. 返回模板详情页面
10. 验证更改已保存

**预期结果**:
- ✅ 能够成功编辑步骤
- ✅ 自定义名称正确显示
- ✅ 时长转换正确（分钟 ↔ 秒）
- ✅ 更改持久化保存

#### 3. 测试添加步骤功能

**测试步骤**:
1. 在模板详情页面点击"添加新步骤"
2. 输入自定义步骤名称
3. 选择步骤类型
4. 设置时长
5. 点击"确认添加"
6. 验证新步骤出现在列表中

**预期结果**:
- ✅ 支持自定义名称
- ✅ 支持时长设置
- ✅ 步骤正确添加到模板

#### 4. 测试权限验证

**测试步骤**:
1. 用户A创建模板和步骤
2. 用户B尝试编辑用户A的步骤
3. 验证权限拒绝

**预期结果**:
- ✅ 只有模板创建者能编辑步骤
- ✅ 其他用户无法看到或编辑

## 🔧 API端点测试

### 使用curl测试API

```bash
# 获取JWT token (替换用户名密码)
TOKEN=$(curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username":"your_username","password":"your_password"}' \
  | jq -r '.access')

# 测试步骤更新
curl -X PATCH http://localhost:8000/api/events/steps/1/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "自定义步骤名称",
    "step_type": "FREE_CHAT",
    "duration": 600
  }'

# 测试步骤重新排序
curl -X POST http://localhost:8000/api/events/templates/1/reorder-steps/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"step_ids": [3, 1, 2]}'
```

## 🐛 已知问题和解决方案

### 1. React Native Reanimated 错误

**问题**: 应用启动时出现worklet错误

**解决方案**: 
- ✅ 已安装react-native-reanimated
- ✅ 已配置babel插件
- ✅ 暂时使用FlatList替代DraggableFlatList

### 2. 前端测试配置

**问题**: Jest配置复杂，TypeScript支持问题

**状态**: 
- ✅ 后端测试完全通过
- ⚠️ 前端测试配置待优化
- ✅ 手动测试可正常进行

## 📊 测试总结

| 功能模块 | 后端API | 前端UI | 状态 |
|---------|---------|--------|------|
| 步骤编辑 | ✅ 通过 | ✅ 实现 | 完成 |
| 自定义名称 | ✅ 通过 | ✅ 实现 | 完成 |
| 时长设置 | ✅ 通过 | ✅ 实现 | 完成 |
| 权限验证 | ✅ 通过 | ✅ 实现 | 完成 |
| 步骤排序 | ✅ 通过 | ⚠️ 简化版 | 基本完成 |

## 🚀 下一步计划

1. **优化拖拽排序**: 完善DraggableFlatList集成
2. **完善测试**: 修复前端测试配置
3. **用户体验**: 添加更多交互反馈
4. **性能优化**: 优化大量步骤时的性能

## 📝 测试报告

**测试日期**: 2025年7月4日
**测试环境**: 开发环境
**测试结果**: 核心功能正常，满足需求规格
