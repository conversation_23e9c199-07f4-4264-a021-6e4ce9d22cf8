/**
 * Tests for Event API functions
 */
import { reorderSteps, getStepDetails, updateStep } from '../src/api/eventApi';

// Mock fetch
global.fetch = jest.fn();
const mockedFetch = fetch;

describe('Event API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('reorderSteps', () => {
    it('sends correct request for reordering steps', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ id: 1, name: 'Test Template' }),
      };
      mockedFetch.mockResolvedValue(mockResponse);

      const result = await reorderSteps('test-token', 1, [3, 1, 2]);

      expect(mockedFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/events/templates/1/reorder-steps/'),
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token',
          },
          body: JSON.stringify({ step_ids: [3, 1, 2] }),
        }
      );

      expect(result).toEqual({ id: 1, name: 'Test Template' });
    });

    it('throws error on failed request', async () => {
      const mockResponse = {
        ok: false,
        json: jest.fn().mockResolvedValue({ error: 'Bad request' }),
      };
      mockedFetch.mockResolvedValue(mockResponse);

      await expect(reorderSteps('test-token', 1, [1, 2, 3])).rejects.toThrow();
    });
  });

  describe('getStepDetails', () => {
    it('fetches step details correctly', async () => {
      const mockStep = {
        id: 1,
        name: 'Test Step',
        order: 1,
        step_type: 'GAME_PICTIONARY',
        configuration: {},
        duration: 300,
      };

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockStep),
      };
      mockedFetch.mockResolvedValue(mockResponse);

      const result = await getStepDetails('test-token', 1);

      expect(mockedFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/events/steps/1/'),
        {
          headers: {
            'Authorization': 'Bearer test-token',
          },
        }
      );

      expect(result).toEqual(mockStep);
    });

    it('throws error when step not found', async () => {
      const mockResponse = {
        ok: false,
        json: jest.fn().mockResolvedValue({ error: 'Not found' }),
      };
      mockedFetch.mockResolvedValue(mockResponse as any);

      await expect(getStepDetails('test-token', 999)).rejects.toThrow();
    });
  });

  describe('updateStep', () => {
    it('updates step correctly', async () => {
      const mockStep = {
        id: 1,
        name: 'Updated Step',
        order: 1,
        step_type: 'FREE_CHAT',
        configuration: {},
        duration: 600,
      };

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockStep),
      };
      mockedFetch.mockResolvedValue(mockResponse as any);

      const updateData = {
        name: 'Updated Step',
        step_type: 'FREE_CHAT' as const,
        duration: 600,
      };

      const result = await updateStep('test-token', 1, updateData);

      expect(mockedFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/events/steps/1/'),
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token',
          },
          body: JSON.stringify(updateData),
        }
      );

      expect(result).toEqual(mockStep);
    });

    it('handles validation errors', async () => {
      const mockResponse = {
        ok: false,
        json: jest.fn().mockResolvedValue({
          step_type: ['Invalid choice'],
          duration: ['Must be positive'],
        }),
      };
      mockedFetch.mockResolvedValue(mockResponse as any);

      const updateData = {
        step_type: 'INVALID_TYPE' as any,
        duration: -100,
      };

      await expect(updateStep('test-token', 1, updateData)).rejects.toThrow();
    });
  });
});
