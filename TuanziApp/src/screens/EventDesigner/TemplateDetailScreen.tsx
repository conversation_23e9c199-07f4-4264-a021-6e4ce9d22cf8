import React, { useState, useCallback } from 'react';
import { View, Text, Button, StyleSheet, Alert, ActivityIndicator, TouchableOpacity, FlatList } from 'react-native';
import { useRoute, RouteProp, useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { getTemplateDetails } from '../../api/eventApi';
import { EventTemplate, RootStackParamList, EventStep } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type TemplateDetailRouteProp = RouteProp<RootStackParamList, 'TemplateDetail'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'TemplateDetail'>;

export const TemplateDetailScreen = () => {
    const route = useRoute<TemplateDetailRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const { templateId } = route.params;
    const { token } = useAuth();

    const [template, setTemplate] = useState<EventTemplate | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [steps, setSteps] = useState<EventStep[]>([]);

    const fetchDetails = useCallback(async () => {
        if (!token) return;
        try {
            setIsLoading(true);
            const data = await getTemplateDetails(token, templateId);
            setTemplate(data);
            setSteps(data.steps);
        } catch (error) {
            Alert.alert('错误', '无法加载模板详情。');
        } finally {
            setIsLoading(false);
        }
    }, [token, templateId]);

    useFocusEffect(useCallback(() => { fetchDetails(); }, [fetchDetails]));

    const getStepDisplayName = (step: EventStep) => {
        if (step.name && step.name.trim()) {
            return step.name;
        }
        // Fallback to step type display name
        switch (step.step_type) {
            case 'GAME_PICTIONARY':
                return '游戏：你画我猜';
            case 'FREE_CHAT':
                return '自由讨论';
            default:
                return step.step_type.replace(/_/g, ' ');
        }
    };

    const renderStepItem = ({ item }: { item: EventStep }) => (
        <TouchableOpacity
            style={styles.stepCard}
            onPress={() => navigation.navigate('EditStep', { stepId: item.id })}
        >
            <View style={styles.stepContent}>
                <Text style={styles.stepOrder}>第 {item.order} 步</Text>
                <Text style={styles.stepName}>{getStepDisplayName(item)}</Text>
                <Text style={styles.stepDuration}>{item.duration / 60} 分钟</Text>
            </View>
            <Text style={styles.editHint}>点击编辑</Text>
        </TouchableOpacity>
    );

    if (isLoading) {
        return <View style={commonStyles.container}><ActivityIndicator size="large" /></View>;
    }

    if (!template) {
        return <View style={commonStyles.container}><Text>未找到模板。</Text></View>;
    }

    return (
        <View style={styles.container}>
            <FlatList
                data={steps}
                renderItem={renderStepItem}
                keyExtractor={(item) => item.id.toString()}
                ListHeaderComponent={
                    <View style={styles.header}>
                        <Text style={commonStyles.title}>{template.name}</Text>
                        <Text style={styles.description}>{template.description}</Text>
                    </View>
                }
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyText}>这个模板还没有任何步骤。</Text>
                    </View>
                }
                contentContainerStyle={styles.listContainer}
            />

            <View style={styles.buttonContainer}>
                <Button title="添加新步骤" onPress={() => navigation.navigate('AddStep', { templateId })} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: '#f5f5f5' },
    header: { padding: 20, backgroundColor: '#fff', borderBottomWidth: 1, borderColor: '#eee' },
    description: { fontSize: 16, color: '#666', marginTop: 10, textAlign: 'center' },
    listContainer: { paddingVertical: 10 },
    buttonContainer: { padding: 20, backgroundColor: '#fff' },
    emptyContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    stepCard: {
        backgroundColor: '#fff',
        padding: 15,
        marginVertical: 6,
        marginHorizontal: 16,
        borderRadius: 12,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
    },
    activeStepCard: {
        elevation: 8,
        shadowOpacity: 0.3,
        transform: [{ scale: 1.02 }],
    },
    stepContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    stepOrder: { fontSize: 16, fontWeight: 'bold', color: '#007AFF' },
    stepName: { fontSize: 16, color: '#333', flex: 1, marginHorizontal: 10 },
    stepDuration: { fontSize: 14, color: '#888' },
    editHint: { fontSize: 12, color: '#999', textAlign: 'center', fontStyle: 'italic' },
    emptyText: { textAlign: 'center', fontSize: 16, color: '#888' },
});
