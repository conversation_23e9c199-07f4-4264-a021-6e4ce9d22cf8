{"name": "TuanziApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "config:ip": "node scripts/config-ip.js", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "backend": "cd ../ && source venv/bin/activate && daphne -b 0.0.0.0 -p 8000 Tuanzi_Backend.asgi:application", "dev": "concurrently \"npm start\" \"npm run backend\""}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/cli": "latest", "@react-native/new-app-screen": "0.80.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "jwt-decode": "^4.0.0", "react": "19.1.0", "react-native": "0.80.0", "react-native-draggable-flatlist": "^4.0.3", "react-native-gesture-handler": "^2.26.0", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "concurrently": "^9.2.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}